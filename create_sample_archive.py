# -*- coding: utf-8 -*-
"""
إنشاء بيانات تجريبية للأرشيف
Create Sample Archive Data
"""

import sys
from pathlib import Path
from datetime import datetime, timedelta

# إعداد المسارات
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_sample_letters():
    """إنشاء خطابات تجريبية للأرشيف"""
    print("📝 إنشاء خطابات تجريبية للأرشيف...")
    
    try:
        from database.database_manager import DatabaseManager
        from database.models import Letter, Tenant, LetterTemplate
        
        db_manager = DatabaseManager()
        
        # التحقق من وجود مستأجرين
        tenants = db_manager.get_all_tenants()
        if not tenants:
            print("⚠️ لا يوجد مستأجرين، سيتم إنشاء مستأجرين تجريبيين أولاً...")
            create_sample_tenants(db_manager)
            tenants = db_manager.get_all_tenants()
        
        # التحقق من وجود قوالب
        templates = db_manager.get_all_templates()
        if not templates:
            print("⚠️ لا يوجد قوالب، سيتم إنشاء قوالب تجريبية أولاً...")
            create_sample_templates(db_manager)
            templates = db_manager.get_all_templates()
        
        # إنشاء خطابات تجريبية
        sample_letters = []
        
        # خطابات مختلفة بتواريخ مختلفة
        dates = [
            datetime.now() - timedelta(days=1),
            datetime.now() - timedelta(days=5),
            datetime.now() - timedelta(days=10),
            datetime.now() - timedelta(days=15),
            datetime.now() - timedelta(days=20),
        ]
        
        letter_types = [
            "خطاب إنذار أول",
            "خطاب إنذار ثاني",
            "خطاب إخلاء",
            "خطاب تذكير بالدفع",
            "خطاب تجديد العقد"
        ]
        
        for i, (tenant, template, date, letter_type) in enumerate(zip(tenants[:5], templates[:5], dates, letter_types)):
            letter = Letter()
            letter.tenant_id = tenant.id
            letter.template_id = template.id
            letter.letter_number = f"خطاب-{date.strftime('%Y%m%d')}-{i+1:03d}"
            letter.content = f"محتوى {letter_type} للمستأجر {tenant.name}"
            letter.output_path_docx = f"output/{letter.letter_number}.docx"
            letter.output_path_pdf = f"output/{letter.letter_number}.pdf"
            letter.created_at = date.strftime("%Y-%m-%d %H:%M:%S")
            
            sample_letters.append(letter)
        
        # إضافة الخطابات لقاعدة البيانات
        added_count = 0
        for letter in sample_letters:
            try:
                letter_id = db_manager.add_letter(letter)
                if letter_id > 0:
                    added_count += 1
                    print(f"   ✅ تم إضافة خطاب: {letter.letter_number}")
            except Exception as e:
                print(f"   ❌ خطأ في إضافة خطاب: {e}")
        
        print(f"✅ تم إنشاء {added_count} خطاب تجريبي للأرشيف")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الخطابات التجريبية: {e}")
        return False

def create_sample_tenants(db_manager):
    """إنشاء مستأجرين تجريبيين"""
    print("👥 إنشاء مستأجرين تجريبيين...")
    
    sample_tenants = [
        {
            "name": "أحمد محمد العلي",
            "unit_number": "101",
            "building_number": "A",
            "phone_number": "0501234567",
            "email": "<EMAIL>",
            "notes": "مستأجر منتظم"
        },
        {
            "name": "فاطمة عبدالرحمن",
            "unit_number": "205",
            "building_number": "B",
            "phone_number": "0509876543",
            "email": "<EMAIL>",
            "notes": "مستأجرة قديمة"
        },
        {
            "name": "محمد عبدالله الأحمد",
            "unit_number": "310",
            "building_number": "C",
            "phone_number": "0551122334",
            "email": "<EMAIL>",
            "notes": "تم تجديد العقد"
        },
        {
            "name": "عائشة حسن علي",
            "unit_number": "102",
            "building_number": "A",
            "phone_number": "0555667788",
            "email": "<EMAIL>",
            "notes": "مستأجرة جديدة"
        },
        {
            "name": "يوسف أحمد محمد",
            "unit_number": "208",
            "building_number": "B",
            "phone_number": "0544556677",
            "email": "<EMAIL>",
            "notes": "بحاجة لمتابعة"
        }
    ]
    
    for tenant_data in sample_tenants:
        tenant = Tenant()
        tenant.name = tenant_data["name"]
        tenant.unit_number = tenant_data["unit_number"]
        tenant.building_number = tenant_data["building_number"]
        tenant.phone_number = tenant_data["phone_number"]
        tenant.email = tenant_data["email"]
        tenant.notes = tenant_data["notes"]
        
        try:
            db_manager.add_tenant(tenant)
            print(f"   ✅ تم إضافة مستأجر: {tenant.name}")
        except:
            pass  # المستأجر موجود بالفعل

def create_sample_templates(db_manager):
    """إنشاء قوالب تجريبية"""
    print("📄 إنشاء قوالب تجريبية...")
    
    sample_templates = [
        {
            "name": "خطاب إنذار أول",
            "template_type": "إنذار",
            "description": "قالب للإنذار الأول للمستأجر"
        },
        {
            "name": "خطاب إنذار ثاني",
            "template_type": "إنذار",
            "description": "قالب للإنذار الثاني للمستأجر"
        },
        {
            "name": "خطاب إخلاء",
            "template_type": "إخلاء",
            "description": "قالب لطلب إخلاء الوحدة"
        },
        {
            "name": "خطاب تذكير بالدفع",
            "template_type": "تذكير",
            "description": "قالب لتذكير المستأجر بالدفع"
        },
        {
            "name": "خطاب تجديد العقد",
            "template_type": "تجديد",
            "description": "قالب لتجديد عقد الإيجار"
        }
    ]
    
    for template_data in sample_templates:
        template = LetterTemplate()
        template.name = template_data["name"]
        template.template_type = template_data["template_type"]
        template.file_path = f"templates/{template_data['name']}.docx"
        template.description = template_data["description"]
        template.is_active = True
        
        try:
            db_manager.add_template(template)
            print(f"   ✅ تم إضافة قالب: {template.name}")
        except:
            pass  # القالب موجود بالفعل

def main():
    """الدالة الرئيسية"""
    print("📝 إنشاء بيانات تجريبية للأرشيف")
    print("=" * 50)
    
    if create_sample_letters():
        print("\n✅ تم إنشاء البيانات التجريبية بنجاح!")
        print("\nالآن يمكنك:")
        print("1. تشغيل التطبيق")
        print("2. الانتقال لتبويب الأرشيف")
        print("3. مشاهدة الخطابات التجريبية")
        print("4. اختبار فلترة التواريخ")
        
        print("\nلتشغيل التطبيق:")
        print("python run_arabic_perfect.py")
        
    else:
        print("\n❌ فشل في إنشاء البيانات التجريبية")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    input("اضغط Enter للخروج...")
    sys.exit(exit_code)
