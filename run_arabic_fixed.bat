@echo off
chcp 65001 >nul
title نظام إدارة خطابات المستأجرين - إصلاح النصوص العربية

echo.
echo ================================================================
echo    نظام إدارة خطابات المستأجرين
echo    إصلاح النصوص العربية - RTL كامل
echo ================================================================
echo.

echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث من python.org
    echo.
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python %PYTHON_VERSION% متوفر

echo.
echo 🔤 تشغيل التطبيق مع إصلاح النصوص العربية...
echo.

python run_arabic_fixed.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق
    echo.
    echo 💡 جرب الحلول التالية:
    echo.
    echo 1. تثبيت مكتبات اللغة العربية:
    echo    pip install arabic-reshaper python-bidi
    echo.
    echo 2. تثبيت جميع المكتبات:
    echo    pip install -r requirements.txt
    echo.
    echo 3. التشغيل البديل:
    echo    python run_final.py
    echo.
    echo 4. التشغيل التقليدي:
    echo    python start_app.py
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق التطبيق بنجاح
    echo 🔤 النصوص العربية تعمل بشكل مثالي!
)

echo.
pause
