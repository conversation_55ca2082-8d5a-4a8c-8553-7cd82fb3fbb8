# -*- coding: utf-8 -*-
"""
إصلاح مشكلة الأرشيف الفارغ
Fix Empty Archive Issue
"""

import sys
from pathlib import Path

# إعداد المسارات
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_archive_functionality():
    """اختبار وظائف الأرشيف"""
    print("🔍 اختبار وظائف الأرشيف...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # اختبار الاتصال بقاعدة البيانات
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # فحص جدول الخطابات
        letters = db_manager.get_all_letters()
        print(f"📋 عدد الخطابات في قاعدة البيانات: {len(letters)}")
        
        if letters:
            print("📝 الخطابات الموجودة:")
            for i, letter in enumerate(letters[:5], 1):
                print(f"   {i}. خطاب رقم: {letter.letter_number or f'خطاب-{letter.id}'}")
                print(f"      تاريخ الإنشاء: {letter.created_at}")
        else:
            print("⚠️ لا توجد خطابات في قاعدة البيانات")
            return False
        
        # اختبار فلترة التواريخ
        from datetime import datetime, timedelta
        from_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        to_date = datetime.now().strftime("%Y-%m-%d")
        
        filtered_letters = db_manager.get_letters_by_date_range(from_date, to_date)
        print(f"📅 الخطابات في آخر 30 يوم: {len(filtered_letters)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأرشيف: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_data():
    """إنشاء بيانات اختبار للأرشيف"""
    print("📝 إنشاء بيانات اختبار للأرشيف...")
    
    try:
        from database.database_manager import DatabaseManager
        from database.models import Letter, Tenant, LetterTemplate
        from datetime import datetime, timedelta
        
        db_manager = DatabaseManager()
        
        # إنشاء مستأجر تجريبي إذا لم يوجد
        tenants = db_manager.get_all_tenants()
        if not tenants:
            tenant = Tenant()
            tenant.name = "أحمد محمد (تجريبي)"
            tenant.unit_number = "101"
            tenant.building_number = "A"
            tenant.phone_number = "0501234567"
            tenant.email = "<EMAIL>"
            tenant.notes = "مستأجر تجريبي للاختبار"
            
            tenant_id = db_manager.add_tenant(tenant)
            print(f"✅ تم إنشاء مستأجر تجريبي: {tenant.name}")
        else:
            tenant_id = tenants[0].id
            print(f"✅ استخدام مستأجر موجود: {tenants[0].name}")
        
        # إنشاء قالب تجريبي إذا لم يوجد
        templates = db_manager.get_all_templates()
        if not templates:
            template = LetterTemplate()
            template.name = "خطاب تجريبي"
            template.template_type = "اختبار"
            template.file_path = "templates/test.docx"
            template.description = "قالب تجريبي للاختبار"
            template.is_active = True
            
            template_id = db_manager.add_template(template)
            print(f"✅ تم إنشاء قالب تجريبي: {template.name}")
        else:
            template_id = templates[0].id
            print(f"✅ استخدام قالب موجود: {templates[0].name}")
        
        # إنشاء خطابات تجريبية
        test_letters = []
        dates = [
            datetime.now(),
            datetime.now() - timedelta(days=1),
            datetime.now() - timedelta(days=7),
            datetime.now() - timedelta(days=14),
            datetime.now() - timedelta(days=21)
        ]
        
        for i, date in enumerate(dates, 1):
            letter = Letter()
            letter.tenant_id = tenant_id
            letter.template_id = template_id
            letter.letter_number = f"تجريبي-{date.strftime('%Y%m%d')}-{i:03d}"
            letter.content = f"محتوى خطاب تجريبي رقم {i}"
            letter.output_path_docx = f"output/{letter.letter_number}.docx"
            letter.output_path_pdf = f"output/{letter.letter_number}.pdf"
            letter.created_at = date.strftime("%Y-%m-%d %H:%M:%S")
            
            test_letters.append(letter)
        
        # إضافة الخطابات لقاعدة البيانات
        added_count = 0
        for letter in test_letters:
            try:
                letter_id = db_manager.add_letter(letter)
                if letter_id > 0:
                    added_count += 1
                    print(f"   ✅ تم إضافة: {letter.letter_number}")
            except Exception as e:
                print(f"   ⚠️ خطأ في إضافة {letter.letter_number}: {e}")
        
        print(f"✅ تم إنشاء {added_count} خطاب تجريبي")
        return added_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_archive_ui():
    """اختبار واجهة الأرشيف"""
    print("🖥️ اختبار واجهة الأرشيف...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.main_window import MainWindow
        from utils.arabic_support import ArabicSupport
        
        app = QApplication(sys.argv)
        
        # إعداد RTL
        ArabicSupport.setup_application_rtl(app)
        
        # إنشاء النافذة
        window = MainWindow()
        
        # التحقق من وجود تبويب الأرشيف
        if hasattr(window, 'archive_table'):
            print("✅ تم العثور على جدول الأرشيف")
            
            # تحميل الأرشيف
            window.load_archive()
            
            # فحص عدد الصفوف
            row_count = window.archive_table.rowCount()
            print(f"📋 عدد الصفوف في جدول الأرشيف: {row_count}")
            
            if row_count > 0:
                print("✅ الأرشيف يحتوي على بيانات")
                
                # عرض النافذة لمدة قصيرة
                window.show()
                window.raise_()
                window.activateWindow()
                
                print("🎯 النافذة ستظهر لمدة 3 ثوان...")
                
                # إغلاق تلقائي
                from PySide6.QtCore import QTimer
                QTimer.singleShot(3000, app.quit)
                
                app.exec()
                
                return True
            else:
                print("⚠️ الأرشيف فارغ")
                return False
        else:
            print("❌ لم يتم العثور على جدول الأرشيف")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة الأرشيف: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح مشكلة الأرشيف الفارغ")
    print("=" * 50)
    
    # اختبار وظائف الأرشيف
    if not test_archive_functionality():
        print("\n📝 إنشاء بيانات تجريبية...")
        if not create_test_data():
            print("❌ فشل في إنشاء البيانات التجريبية")
            return 1
    
    # إعادة اختبار بعد إنشاء البيانات
    print("\n🔄 إعادة اختبار الأرشيف...")
    if test_archive_functionality():
        print("✅ الأرشيف يعمل بشكل صحيح")
        
        # اختبار الواجهة
        print("\n🖥️ اختبار الواجهة...")
        if test_archive_ui():
            print("✅ واجهة الأرشيف تعمل بشكل صحيح")
        else:
            print("⚠️ مشكلة في واجهة الأرشيف")
    else:
        print("❌ مشكلة في وظائف الأرشيف")
        return 1
    
    print("\n" + "=" * 50)
    print("🎉 تم إصلاح مشكلة الأرشيف!")
    print("\nالآن:")
    print("✅ الأرشيف يحتوي على بيانات")
    print("✅ فلترة التواريخ تعمل")
    print("✅ النصوص العربية تظهر بشكل صحيح")
    
    print("\nلتشغيل التطبيق:")
    print("python run_arabic_perfect.py")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    
    if exit_code == 0:
        print("\n✅ تم الإصلاح بنجاح")
    else:
        print("\n❌ حدث خطأ في الإصلاح")
    
    input("اضغط Enter للخروج...")
    sys.exit(exit_code)
