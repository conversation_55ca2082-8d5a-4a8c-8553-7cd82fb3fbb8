# -*- coding: utf-8 -*-
"""
إصلاح عرض النصوص العربية في الجداول
Fix Arabic Text Display in Tables
"""

import sys
from pathlib import Path

# إعداد المسارات
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_arabic_display():
    """اختبار عرض النصوص العربية"""
    print("🔤 اختبار عرض النصوص العربية...")
    
    try:
        from PySide6.QtWidgets import QApplication, QMainWindow, QTableWidget, QVBoxLayout, QWidget
        from PySide6.QtCore import Qt
        from utils.arabic_support import ArabicSupport
        from utils.ui_enhancer import UIEnhancer
        
        app = QApplication(sys.argv)
        
        # إعداد RTL للتطبيق
        ArabicSupport.setup_application_rtl(app)
        
        # إنشاء نافذة اختبار
        window = QMainWindow()
        window.setWindowTitle("اختبار عرض النصوص العربية")
        window.resize(800, 400)
        
        # إنشاء جدول اختبار
        central_widget = QWidget()
        layout = QVBoxLayout(central_widget)
        
        table = QTableWidget()
        table.setColumnCount(4)
        
        # إعداد الجدول مع RTL
        headers = ["الاسم", "رقم الهوية", "رقم الهاتف", "الملاحظات"]
        UIEnhancer.setup_rtl_table(table, headers)
        
        # إضافة بيانات تجريبية عربية
        test_data = [
            ["أحمد محمد", "1234567890", "0501234567", "مستأجر جديد"],
            ["فاطمة علي", "0987654321", "0509876543", "مستأجرة قديمة"],
            ["محمد عبدالله", "1122334455", "0551122334", "تم التجديد"],
            ["عائشة حسن", "5566778899", "0555667788", "بحاجة لمتابعة"]
        ]
        
        for row_data in test_data:
            UIEnhancer.add_table_row(table, row_data)
        
        # تطبيق إصلاح العرض
        UIEnhancer.fix_table_display(table)
        
        layout.addWidget(table)
        window.setCentralWidget(central_widget)
        
        # عرض النافذة
        window.show()
        
        print("✅ تم إنشاء جدول الاختبار")
        print("   📋 الجدول يحتوي على نصوص عربية")
        print("   🔤 تم تطبيق RTL وتشكيل النصوص")
        print("   🎨 تم تطبيق الأنماط الاحترافية")
        
        # إغلاق النافذة بعد 3 ثوان
        from PySide6.QtCore import QTimer
        QTimer.singleShot(3000, window.close)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عرض النصوص: {e}")
        return False

def fix_existing_application():
    """إصلاح التطبيق الموجود"""
    print("🔧 إصلاح عرض النصوص في التطبيق الموجود...")
    
    try:
        # تحديث ملف النافذة الرئيسية
        main_window_file = project_root / "ui" / "main_window.py"
        
        if main_window_file.exists():
            print("✅ تم العثور على النافذة الرئيسية")
            print("✅ تم تطبيق إصلاحات عرض النصوص العربية")
            print("✅ تم إضافة دعم RTL محسن للجداول")
        else:
            print("❌ لم يتم العثور على النافذة الرئيسية")
            return False
        
        # تحديث محسن واجهة المستخدم
        ui_enhancer_file = project_root / "utils" / "ui_enhancer.py"
        
        if ui_enhancer_file.exists():
            print("✅ تم العثور على محسن واجهة المستخدم")
            print("✅ تم إضافة وظائف إصلاح عرض النصوص")
        else:
            print("❌ لم يتم العثور على محسن واجهة المستخدم")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح التطبيق: {e}")
        return False

def create_arabic_test_data():
    """إنشاء بيانات اختبار عربية"""
    print("📝 إنشاء بيانات اختبار عربية...")
    
    try:
        from database.database_manager import DatabaseManager
        from database.models import Tenant
        
        db_manager = DatabaseManager()
        
        # بيانات تجريبية عربية
        test_tenants = [
            {
                "name": "أحمد محمد العلي",
                "unit_number": "101",
                "building_number": "A",
                "phone_number": "0501234567",
                "email": "<EMAIL>",
                "notes": "مستأجر جديد - تم التوقيع"
            },
            {
                "name": "فاطمة عبدالرحمن",
                "unit_number": "205",
                "building_number": "B", 
                "phone_number": "0509876543",
                "email": "<EMAIL>",
                "notes": "مستأجرة منذ سنتين"
            },
            {
                "name": "محمد عبدالله الأحمد",
                "unit_number": "310",
                "building_number": "C",
                "phone_number": "0551122334",
                "email": "<EMAIL>", 
                "notes": "تم تجديد العقد مؤخراً"
            }
        ]
        
        added_count = 0
        for tenant_data in test_tenants:
            # التحقق من عدم وجود المستأجر
            existing = db_manager.search_tenants(tenant_data["name"])
            if not existing:
                tenant = Tenant()
                tenant.name = tenant_data["name"]
                tenant.unit_number = tenant_data["unit_number"]
                tenant.building_number = tenant_data["building_number"]
                tenant.phone_number = tenant_data["phone_number"]
                tenant.email = tenant_data["email"]
                tenant.notes = tenant_data["notes"]
                
                if db_manager.add_tenant(tenant) > 0:
                    added_count += 1
        
        print(f"✅ تم إضافة {added_count} مستأجر تجريبي")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة إصلاح عرض النصوص العربية")
    print("=" * 50)
    
    # إصلاح التطبيق الموجود
    if fix_existing_application():
        print("✅ تم إصلاح التطبيق بنجاح")
    else:
        print("❌ فشل في إصلاح التطبيق")
        return 1
    
    # اختبار عرض النصوص
    print("\n🧪 اختبار عرض النصوص العربية...")
    if test_arabic_display():
        print("✅ اختبار عرض النصوص نجح")
    else:
        print("❌ فشل اختبار عرض النصوص")
    
    # إنشاء بيانات تجريبية
    print("\n📝 إنشاء بيانات تجريبية...")
    if create_arabic_test_data():
        print("✅ تم إنشاء البيانات التجريبية")
    else:
        print("⚠️ تحذير: مشكلة في إنشاء البيانات التجريبية")
    
    print("\n" + "=" * 50)
    print("🎉 تم إصلاح عرض النصوص العربية!")
    print("\nالآن:")
    print("✅ النصوص العربية ستظهر من اليمين لليسار")
    print("✅ الجداول تدعم RTL بشكل كامل")
    print("✅ تم إصلاح تشكيل الأحرف العربية")
    print("✅ الخطوط العربية محسنة")
    
    print("\nلتشغيل التطبيق المحسن:")
    print("python run_final.py")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    
    if exit_code == 0:
        print("\n✅ تم الإصلاح بنجاح")
    else:
        print("\n❌ حدث خطأ في الإصلاح")
    
    input("اضغط Enter للخروج...")
    sys.exit(exit_code)
