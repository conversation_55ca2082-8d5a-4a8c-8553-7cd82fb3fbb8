# -*- coding: utf-8 -*-
"""
تشغيل التطبيق مع إصلاح النصوص العربية
Application Launcher with Arabic Text Fix
"""

import sys
import os
from pathlib import Path

# إعداد المسارات
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """تشغيل التطبيق مع إصلاح النصوص العربية"""
    print("🔤 نظام إدارة خطابات المستأجرين - إصلاح النصوص العربية")
    print("=" * 70)
    
    try:
        # فحص المكتبات المطلوبة
        print("🔍 فحص المكتبات...")
        
        required_modules = [
            ('PySide6', 'PySide6.QtWidgets'),
            ('arabic-reshaper', 'arabic_reshaper'),
            ('python-bidi', 'bidi.algorithm')
        ]
        
        missing = []
        for name, module in required_modules:
            try:
                __import__(module)
                print(f"   ✅ {name}")
            except ImportError:
                print(f"   ❌ {name} - مفقود")
                missing.append(name)
        
        if missing:
            print(f"\n⚠️ مكتبات مفقودة: {', '.join(missing)}")
            print("لتثبيت المكتبات:")
            for module in missing:
                print(f"   pip install {module}")
            return 1
        
        # استيراد المكتبات
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        from utils.arabic_support import ArabicSupport
        from ui.main_window import MainWindow
        from utils.ui_enhancer import UIEnhancer
        
        print("✅ جميع المكتبات متوفرة")
        
        # إنشاء التطبيق
        print("\n🚀 إنشاء التطبيق...")
        app = QApplication(sys.argv)
        
        # إعداد خصائص التطبيق
        app.setApplicationName("نظام إدارة خطابات المستأجرين")
        app.setApplicationVersion("2.0 Arabic Fixed")
        app.setOrganizationName("Desktop as3am 7tab")
        
        # إعداد دعم RTL والعربية الكامل
        print("🔤 إعداد دعم RTL والعربية...")
        if ArabicSupport.setup_application_rtl(app):
            print("   ✅ تم إعداد RTL للتطبيق")
            print("   ✅ تم تطبيق الأنماط الاحترافية")
            print("   ✅ تم إعداد الخطوط العربية")
        else:
            print("   ⚠️ تحذير: مشكلة في إعداد RTL")
        
        # إنشاء النافذة الرئيسية
        print("\n🏠 إنشاء النافذة الرئيسية...")
        window = MainWindow()
        
        # تطبيق إصلاحات إضافية للنصوص العربية
        print("🔧 تطبيق إصلاحات النصوص العربية...")
        
        # تطبيق RTL على النافذة
        window.setLayoutDirection(Qt.RightToLeft)
        
        # إصلاح جدول المستأجرين
        if hasattr(window, 'tenants_table'):
            UIEnhancer.fix_table_display(window.tenants_table)
            print("   ✅ تم إصلاح جدول المستأجرين")
        
        # تطبيق التحسينات الاحترافية
        UIEnhancer.enhance_main_window(window)
        print("   ✅ تم تطبيق التحسينات الاحترافية")
        
        # عرض النافذة
        window.show()
        window.raise_()
        window.activateWindow()
        
        print("✅ تم إنشاء النافذة بنجاح")
        
        print("\n🎉 تم تشغيل التطبيق بنجاح!")
        print("=" * 70)
        print("إصلاحات النصوص العربية المطبقة:")
        print("   ✅ النصوص تظهر من اليمين لليسار")
        print("   ✅ تشكيل الأحرف العربية صحيح")
        print("   ✅ الجداول تدعم RTL كاملاً")
        print("   ✅ الخطوط العربية محسنة")
        print("   ✅ المحاذاة صحيحة في جميع العناصر")
        
        print("\nالميزات المتاحة:")
        print("   📋 إدارة المستأجرين مع نصوص عربية صحيحة")
        print("   📄 إنشاء خطابات بالعربية")
        print("   📱 إرسال واتساب")
        print("   📋 إدارة القوالب (Ctrl+T)")
        print("   ⚙️ الإعدادات (Ctrl+,)")
        
        print("\nنصائح للاستخدام:")
        print("   💡 اكتب النصوص العربية مباشرة - ستظهر بالاتجاه الصحيح")
        print("   💡 الجداول تدعم الترتيب والبحث بالعربية")
        print("   💡 جميع النوافذ تدعم RTL تلقائياً")
        
        # تشغيل التطبيق
        return app.exec()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("\nلحل هذه المشكلة:")
        print("pip install PySide6 python-docx arabic-reshaper python-bidi")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        print("\nجرب الحلول التالية:")
        print("1. python update_app.py")
        print("2. python start_app.py")
        return 1

if __name__ == "__main__":
    exit_code = main()
    
    print("\n" + "=" * 70)
    if exit_code == 0:
        print("✅ تم إغلاق التطبيق بنجاح")
        print("🔤 النصوص العربية تعمل بشكل مثالي!")
    else:
        print(f"❌ التطبيق توقف مع رمز الخطأ: {exit_code}")
        print("💡 للمساعدة، راجع ملف: إصلاح_النصوص_العربية.md")
        input("اضغط Enter للخروج...")
    
    sys.exit(exit_code)
