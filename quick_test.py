# -*- coding: utf-8 -*-
"""
اختبار سريع للتطبيق
Quick Test
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_imports():
    """اختبار الاستيرادات الأساسية"""
    print("اختبار الاستيرادات...")
    
    try:
        import PySide6
        print("✅ PySide6 متوفر")
    except ImportError:
        print("❌ PySide6 غير متوفر")
        return False
    
    try:
        from PySide6.QtWidgets import QApplication, QLabel, QWidget
        from PySide6.QtCore import Qt
        print("✅ PySide6 widgets متوفرة")
    except ImportError as e:
        print(f"❌ خطأ في PySide6 widgets: {e}")
        return False
    
    return True

def test_simple_window():
    """اختبار نافذة بسيطة"""
    print("اختبار نافذة بسيطة...")
    
    try:
        from PySide6.QtWidgets import QApplication, QLabel, QWidget
        from PySide6.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        window = QWidget()
        window.setWindowTitle("اختبار")
        window.resize(300, 200)
        
        label = QLabel("مرحباً! النافذة تعمل بنجاح", window)
        label.setAlignment(Qt.AlignCenter)
        
        window.show()
        
        print("✅ تم إنشاء النافذة - تحقق من شريط المهام")
        
        # تشغيل لمدة قصيرة ثم إغلاق
        from PySide6.QtCore import QTimer
        QTimer.singleShot(3000, app.quit)  # إغلاق بعد 3 ثواني
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في النافذة البسيطة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 40)
    print("اختبار سريع للتطبيق")
    print("=" * 40)
    
    # اختبار الاستيرادات
    if not test_basic_imports():
        print("فشل في اختبار الاستيرادات")
        return 1
    
    # اختبار النافذة البسيطة
    result = test_simple_window()
    
    if result == 0:
        print("✅ الاختبار نجح - التطبيق يعمل!")
    else:
        print("❌ فشل الاختبار")
    
    return result

if __name__ == "__main__":
    sys.exit(main())
