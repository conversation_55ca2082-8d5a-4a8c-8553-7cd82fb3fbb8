# -*- coding: utf-8 -*-
"""
اختبار بسيط للنصوص العربية
Simple Arabic Text Test
"""

import sys
from pathlib import Path

# إعداد المسارات
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """اختبار بسيط للنصوص العربية"""
    print("🔤 اختبار النصوص العربية البسيط")
    print("=" * 40)
    
    try:
        # اختبار استيراد المكتبات
        from PySide6.QtWidgets import QApplication, QMainWindow, QTableWidget, QVBoxLayout, QWidget, QTableWidgetItem
        from PySide6.QtCore import Qt
        
        print("✅ تم استيراد PySide6 بنجاح")
        
        # اختبار دعم اللغة العربية
        try:
            from utils.arabic_support import ArabicSupport
            print("✅ تم استيراد دعم اللغة العربية")
            
            # اختبار تشكيل النص
            test_text = "أحمد محمد"
            reshaped = ArabicSupport.reshape_arabic_text(test_text)
            print(f"✅ تشكيل النص: '{test_text}' -> '{reshaped}'")
            
        except Exception as e:
            print(f"❌ خطأ في دعم اللغة العربية: {e}")
            return 1
        
        # إنشاء تطبيق بسيط
        app = QApplication(sys.argv)
        
        # إعداد RTL
        ArabicSupport.setup_application_rtl(app)
        print("✅ تم إعداد RTL للتطبيق")
        
        # إنشاء نافذة
        window = QMainWindow()
        window.setWindowTitle("اختبار النصوص العربية")
        window.resize(600, 300)
        
        # إنشاء جدول
        central_widget = QWidget()
        layout = QVBoxLayout(central_widget)
        
        table = QTableWidget()
        table.setColumnCount(3)
        table.setRowCount(2)
        
        # إعداد الرؤوس
        table.setHorizontalHeaderLabels(["الاسم", "الهاتف", "الملاحظات"])
        
        # إضافة بيانات عربية
        items = [
            ["أحمد محمد", "0501234567", "مستأجر جديد"],
            ["فاطمة علي", "0509876543", "مستأجرة قديمة"]
        ]
        
        for row, row_data in enumerate(items):
            for col, text in enumerate(row_data):
                # تشكيل النص العربي
                shaped_text = ArabicSupport.reshape_arabic_text(text)
                item = QTableWidgetItem(shaped_text)
                
                # تطبيق RTL
                item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                
                # إعداد الخط
                font = item.font()
                font.setFamily("Tahoma")
                font.setPointSize(11)
                item.setFont(font)
                
                table.setItem(row, col, item)
        
        # تطبيق RTL على الجدول
        table.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد الرأس
        header = table.horizontalHeader()
        header.setLayoutDirection(Qt.RightToLeft)
        
        layout.addWidget(table)
        window.setCentralWidget(central_widget)
        
        # عرض النافذة
        window.show()
        
        print("✅ تم إنشاء النافذة مع النصوص العربية")
        print("📋 الجدول يحتوي على:")
        for i, row_data in enumerate(items):
            print(f"   {i+1}. {' | '.join(row_data)}")
        
        print("\n🎯 النافذة ستظهر لمدة 3 ثوان...")
        
        # إغلاق تلقائي بعد 3 ثوان
        from PySide6.QtCore import QTimer
        QTimer.singleShot(3000, app.quit)
        
        # تشغيل التطبيق
        app.exec()
        
        print("✅ تم إغلاق النافذة بنجاح")
        print("\n🎉 اختبار النصوص العربية نجح!")
        
        return 0
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    
    if exit_code == 0:
        print("\n✅ جميع الاختبارات نجحت")
        print("🚀 يمكنك الآن تشغيل التطبيق الكامل:")
        print("   python run_final.py")
    else:
        print("\n❌ فشل في الاختبار")
        print("💡 تأكد من تثبيت المكتبات:")
        print("   pip install PySide6 arabic-reshaper python-bidi")
    
    sys.exit(exit_code)
