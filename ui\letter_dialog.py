# -*- coding: utf-8 -*-
"""
نافذة إنشاء الخطابات
Letter Creation Dialog
"""

from datetime import datetime
from pathlib import Path

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QComboBox,
    QPushButton, QTextEdit, QLabel, QMessageBox, QGroupBox,
    QFileDialog, QProgressBar, QCheckBox
)
from PySide6.QtCore import Qt, QThread, Signal
from PySide6.QtGui import QFont

from database.database_manager import DatabaseManager
from database.models import Tenant, LetterTemplate, Letter
from utils.arabic_support import ArabicSupport
from services.letter_service import LetterService
from services.whatsapp_service import WhatsAppService
from config.settings import TEMPLATE_TYPES, OUTPUT_DIR


class LetterDialog(QDialog):
    """نافذة إنشاء الخطابات"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.letter_service = LetterService()
        self.whatsapp_service = WhatsAppService()
        self.tenants = []
        self.templates = []

        self.init_ui()
        self.setup_connections()
        self.load_data()

        # إعداد دعم اللغة العربية
        ArabicSupport.setup_arabic_font(self)
        ArabicSupport.setup_rtl_layout(self)

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إنشاء خطاب جديد")
        self.setModal(True)
        self.resize(600, 500)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)

        # مجموعة اختيار المستأجر والقالب
        selection_group = QGroupBox("اختيار المستأجر والقالب")
        selection_layout = QFormLayout(selection_group)

        # اختيار المستأجر
        self.tenant_combo = QComboBox()
        self.tenant_combo.setEditable(True)
        self.tenant_combo.setPlaceholderText("اختر المستأجر...")
        selection_layout.addRow("المستأجر *:", self.tenant_combo)

        # اختيار القالب
        self.template_combo = QComboBox()
        self.template_combo.setPlaceholderText("اختر القالب...")
        selection_layout.addRow("القالب *:", self.template_combo)

        # زر تحميل قالب جديد
        self.load_template_btn = QPushButton("تحميل قالب جديد")
        selection_layout.addRow("", self.load_template_btn)

        main_layout.addWidget(selection_group)

        # مجموعة محتوى الخطاب
        content_group = QGroupBox("محتوى الخطاب")
        content_layout = QVBoxLayout(content_group)

        # نص المحتوى
        content_label = QLabel("محتوى الخطاب *:")
        self.content_input = QTextEdit()
        self.content_input.setPlaceholderText(
            "أدخل محتوى الخطاب هنا...\n"
            "سيتم دمج هذا المحتوى مع القالب المحدد.\n"
            "يمكنك استخدام المتغيرات التالية:\n"
            "{Name} - اسم المستأجر\n"
            "{Unit} - رقم الوحدة\n"
            "{Building} - رقم العمارة\n"
            "{LetterNo} - رقم الخطاب\n"
            "{Date} - تاريخ الخطاب"
        )
        self.content_input.setMinimumHeight(150)

        content_layout.addWidget(content_label)
        content_layout.addWidget(self.content_input)

        main_layout.addWidget(content_group)

        # مجموعة الخيارات
        options_group = QGroupBox("خيارات الإنشاء")
        options_layout = QVBoxLayout(options_group)

        self.create_pdf_check = QCheckBox("إنشاء ملف PDF")
        self.create_pdf_check.setChecked(True)

        self.open_whatsapp_check = QCheckBox("فتح واتساب للإرسال")
        self.open_whatsapp_check.setChecked(False)

        options_layout.addWidget(self.create_pdf_check)
        options_layout.addWidget(self.open_whatsapp_check)

        main_layout.addWidget(options_group)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        self.preview_btn = QPushButton("معاينة")
        self.create_btn = QPushButton("إنشاء الخطاب")
        self.cancel_btn = QPushButton("إلغاء")

        buttons_layout.addStretch()
        buttons_layout.addWidget(self.preview_btn)
        buttons_layout.addWidget(self.create_btn)
        buttons_layout.addWidget(self.cancel_btn)

        main_layout.addLayout(buttons_layout)

        # إعداد دعم اللغة العربية
        ArabicSupport.setup_arabic_font(self.content_input)
        ArabicSupport.setup_rtl_layout(self.content_input)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.load_template_btn.clicked.connect(self.load_new_template)
        self.preview_btn.clicked.connect(self.preview_letter)
        self.create_btn.clicked.connect(self.create_letter)
        self.cancel_btn.clicked.connect(self.reject)

        # التحقق من صحة البيانات
        self.tenant_combo.currentTextChanged.connect(self.validate_form)
        self.template_combo.currentTextChanged.connect(self.validate_form)
        self.content_input.textChanged.connect(self.validate_form)

        # التحقق الأولي
        self.validate_form()

    def load_data(self):
        """تحميل البيانات المطلوبة"""
        try:
            # تحميل المستأجرين
            self.tenants = self.db_manager.get_all_tenants()
            self.tenant_combo.clear()

            for tenant in self.tenants:
                display_text = f"{tenant.name} - وحدة {tenant.unit_number} - عمارة {tenant.building_number}"
                self.tenant_combo.addItem(ArabicSupport.reshape_arabic_text(display_text), tenant.id)

            # تحميل القوالب
            self.templates = self.db_manager.get_all_templates()
            self.template_combo.clear()

            for template in self.templates:
                display_text = f"{template.name} ({template.template_type})"
                self.template_combo.addItem(ArabicSupport.reshape_arabic_text(display_text), template.id)

            if not self.templates:
                # إضافة خيار افتراضي إذا لم توجد قوالب
                self.template_combo.addItem("قالب افتراضي", -1)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات:\n{e}")

    def validate_form(self):
        """التحقق من صحة النموذج"""
        has_tenant = self.tenant_combo.currentIndex() >= 0
        has_template = self.template_combo.currentIndex() >= 0
        has_content = bool(self.content_input.toPlainText().strip())

        is_valid = has_tenant and has_template and has_content

        self.preview_btn.setEnabled(is_valid)
        self.create_btn.setEnabled(is_valid)

        return is_valid

    def get_selected_tenant(self) -> Tenant:
        """الحصول على المستأجر المحدد"""
        current_index = self.tenant_combo.currentIndex()
        if current_index < 0:
            return None

        tenant_id = self.tenant_combo.itemData(current_index)
        for tenant in self.tenants:
            if tenant.id == tenant_id:
                return tenant

        return None

    def get_selected_template(self) -> LetterTemplate:
        """الحصول على القالب المحدد"""
        current_index = self.template_combo.currentIndex()
        if current_index < 0:
            return None

        template_id = self.template_combo.itemData(current_index)
        if template_id == -1:  # القالب الافتراضي
            return None

        for template in self.templates:
            if template.id == template_id:
                return template

        return None

    def load_new_template(self):
        """تحميل قالب جديد"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختر ملف القالب", "", "ملفات Word (*.docx)"
        )

        if file_path:
            # TODO: إضافة القالب إلى قاعدة البيانات
            QMessageBox.information(self, "قريباً", "ستتوفر هذه الميزة قريباً")

    def preview_letter(self):
        """معاينة الخطاب"""
        if not self.validate_form():
            return

        tenant = self.get_selected_tenant()
        content = self.content_input.toPlainText().strip()

        # إنشاء معاينة بسيطة
        letter_number = self.db_manager.generate_letter_number()
        current_date = ArabicSupport.format_arabic_date(datetime.now())

        preview_text = f"""
معاينة الخطاب:

رقم الخطاب: {letter_number}
التاريخ: {current_date}
المستأجر: {tenant.name}
رقم الوحدة: {tenant.unit_number}
رقم العمارة: {tenant.building_number}

المحتوى:
{content}
        """

        QMessageBox.information(
            self, "معاينة الخطاب",
            ArabicSupport.reshape_arabic_text(preview_text.strip())
        )

    def create_letter(self):
        """إنشاء الخطاب"""
        if not self.validate_form():
            QMessageBox.warning(self, "خطأ", "يرجى ملء جميع الحقول المطلوبة")
            return

        try:
            # إظهار شريط التقدم
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            tenant = self.get_selected_tenant()
            template = self.get_selected_template()
            content = self.content_input.toPlainText().strip()

            # إنشاء كائن الخطاب
            letter = Letter()
            letter.tenant_id = tenant.id
            letter.template_id = template.id if template else 0
            letter.letter_number = self.db_manager.generate_letter_number()
            letter.content = content

            self.progress_bar.setValue(25)

            # إنشاء الخطاب باستخدام الخدمة
            result = self.letter_service.create_letter_from_template(
                tenant, template, content, letter.letter_number
            )

            self.progress_bar.setValue(50)

            if not result['success']:
                raise Exception(result['error'])

            letter.output_path_docx = result['docx_path']

            # إنشاء ملف PDF إذا كان مطلوباً
            if self.create_pdf_check.isChecked():
                pdf_path = self.letter_service.convert_to_pdf(result['docx_path'])
                if pdf_path:
                    letter.output_path_pdf = pdf_path

            self.progress_bar.setValue(75)

            # حفظ الخطاب في قاعدة البيانات
            letter_id = self.db_manager.add_letter(letter)

            self.progress_bar.setValue(100)

            if letter_id > 0:
                QMessageBox.information(
                    self, "نجح",
                    f"تم إنشاء الخطاب بنجاح\nرقم الخطاب: {letter.letter_number}"
                )

                # فتح واتساب إذا كان مطلوباً
                if self.open_whatsapp_check.isChecked():
                    self.whatsapp_service.send_letter(
                        tenant,
                        letter.output_path_pdf or letter.output_path_docx
                    )

                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ الخطاب")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء الخطاب:\n{e}")
        finally:
            self.progress_bar.setVisible(False)


