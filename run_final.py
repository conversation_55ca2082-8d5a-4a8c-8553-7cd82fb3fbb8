# -*- coding: utf-8 -*-
"""
تشغيل نهائي للتطبيق مع دعم RTL كامل وواجهة احترافية
Final Application Launcher with Full RTL Support and Professional UI
"""

import sys
import os
from pathlib import Path

# إعداد المسارات
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات...")
    
    required_modules = [
        ('PySide6', 'PySide6.QtWidgets'),
        ('python-docx', 'docx'),
        ('arabic-reshaper', 'arabic_reshaper'),
        ('python-bidi', 'bidi.algorithm'),
        ('Pillow', 'PIL')
    ]
    
    missing = []
    for name, module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {name}")
        except ImportError:
            print(f"   ❌ {name} - مفقود")
            missing.append(name)
    
    if missing:
        print(f"\n⚠️ مكتبات مفقودة: {', '.join(missing)}")
        print("لتثبيت المكتبات:")
        for module in missing:
            print(f"   pip install {module}")
        return False
    
    return True

def setup_enhanced_application():
    """إعداد التطبيق مع جميع التحسينات"""
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        from utils.arabic_support import ArabicSupport
        
        print("🚀 إنشاء التطبيق...")
        app = QApplication(sys.argv)
        
        # إعداد خصائص التطبيق
        app.setApplicationName("نظام إدارة خطابات المستأجرين")
        app.setApplicationVersion("2.0 RTL Professional")
        app.setOrganizationName("Desktop as3am 7tab")
        
        print("🔤 إعداد دعم RTL والعربية الكامل...")
        if ArabicSupport.setup_application_rtl(app):
            print("   ✅ تم إعداد RTL بنجاح")
            print("   ✅ تم تطبيق الأنماط الاحترافية")
            print("   ✅ تم إعداد الخطوط العربية")
        else:
            print("   ⚠️ تحذير: مشكلة في إعداد RTL")
        
        return app
        
    except Exception as e:
        print(f"❌ خطأ في إعداد التطبيق: {e}")
        return None

def create_enhanced_main_window(app):
    """إنشاء النافذة الرئيسية المحسنة"""
    try:
        from ui.main_window import MainWindow
        from utils.ui_enhancer import UIEnhancer
        from PySide6.QtCore import Qt
        
        print("🏠 إنشاء النافذة الرئيسية...")
        window = MainWindow()
        
        # تطبيق RTL على النافذة
        window.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق التحسينات الاحترافية
        print("🎨 تطبيق التحسينات الاحترافية...")
        UIEnhancer.enhance_main_window(window)
        
        # عرض النافذة
        window.show()
        window.raise_()
        window.activateWindow()
        
        print("✅ تم إنشاء النافذة بنجاح")
        return window
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النافذة: {e}")
        return None

def main():
    """الدالة الرئيسية"""
    print("🌟 نظام إدارة خطابات المستأجرين - الإصدار النهائي")
    print("🎯 دعم RTL كامل + واجهة احترافية")
    print("=" * 70)
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ فشل في فحص المتطلبات")
        input("اضغط Enter للخروج...")
        return 1
    
    print("✅ جميع المتطلبات متوفرة")
    
    # إعداد التطبيق
    app = setup_enhanced_application()
    if not app:
        print("❌ فشل في إعداد التطبيق")
        return 1
    
    # إنشاء النافذة الرئيسية
    window = create_enhanced_main_window(app)
    if not window:
        print("❌ فشل في إنشاء النافذة")
        return 1
    
    print("\n🎉 تم تشغيل التطبيق بنجاح!")
    print("=" * 70)
    print("المميزات المفعلة:")
    print("   ✅ دعم RTL كامل (النصوص من اليمين لليسار)")
    print("   ✅ خطوط عربية محسنة (Tahoma + Anti-aliasing)")
    print("   ✅ واجهة احترافية مع أنماط CSS متقدمة")
    print("   ✅ أزرار ملونة حسب الوظيفة")
    print("   ✅ جداول محسنة مع دعم RTL")
    print("   ✅ قوائم وحقول نص بالعربية")
    print("   ✅ تصميم متجاوب ومتسق")
    
    print("\nالاختصارات المتاحة:")
    print("   📋 Ctrl+T - إدارة القوالب")
    print("   ⚙️ Ctrl+, - الإعدادات")
    print("   👥 Ctrl+N - مستأجر جديد")
    print("   📄 Ctrl+L - خطاب جديد")
    
    print("\nنصائح للاستخدام:")
    print("   💡 جميع النصوص تُكتب من اليمين لليسار تلقائياً")
    print("   💡 الأزرار ملونة: أخضر للحفظ، أحمر للحذف، أزرق للمعلومات")
    print("   💡 استخدم الإعدادات لتخصيص الخطوط والألوان")
    print("   💡 يمكن إضافة قوالب Word مخصصة من إدارة القوالب")
    
    # تشغيل التطبيق
    try:
        return app.exec()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف التطبيق بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    
    print("\n" + "=" * 70)
    if exit_code == 0:
        print("✅ تم إغلاق التطبيق بنجاح")
        print("🙏 شكراً لاستخدام نظام إدارة خطابات المستأجرين")
    else:
        print(f"❌ التطبيق توقف مع رمز الخطأ: {exit_code}")
        print("💡 جرب الحلول التالية:")
        print("   1. python update_app.py")
        print("   2. pip install -r requirements.txt")
        print("   3. python start_app.py")
        input("اضغط Enter للخروج...")
    
    sys.exit(exit_code)
