# 🏢 نظام إدارة خطابات المستأجرين - الإصدار 2.0

## 🎉 مرحباً بك في الإصدار الجديد!

تم تطوير نظام إدارة خطابات المستأجرين ليصبح أكثر قوة واحترافية مع ميزات جديدة رائعة!

---

## ✨ الميزات الجديدة في الإصدار 2.0

### 🎯 1. إدارة القوالب المتقدمة
- **إضافة قوالب جديدة** من ملفات Word موجودة
- **تعديل وحذف القوالب** بسهولة
- **فحص صحة القوالب** تلقائياً
- **تصنيف القوالب** حسب النوع
- **واجهة احترافية** مع شريط أدوات متقدم

### ⚙️ 2. نظام الإعدادات الشامل
- **4 تبويبات منظمة**: عام، مظهر، خطابات، متقدم
- **تخصيص الخطوط والألوان**
- **إعدادات واتساب مخصصة**
- **حفظ الإعدادات** تلقائياً
- **معاينة فورية** للتغييرات

### 🔤 3. دعم محسن للغة العربية
- **خطوط عربية متعددة**: Tahoma, Arial Unicode MS, Segoe UI
- **اتجاه RTL محسن** من اليمين لليسار
- **إصلاح عرض النصوص المعقدة**
- **تحسين جودة الخط** مع Anti-aliasing

### 🎨 4. واجهة مستخدم احترافية
- **تصميم موحد** لجميع النوافذ
- **أزرار ملونة** حسب الوظيفة
- **اختصارات لوحة مفاتيح** للوصول السريع
- **رسائل حالة واضحة**

---

## 🚀 طرق التشغيل

### الطريقة الأولى (مستحسنة):
```bash
# تحديث التطبيق أولاً
python update_app.py

# تشغيل الإصدار المحسن
python run_v2.py
```

### الطريقة الثانية:
```bash
# انقر نقراً مزدوجاً على:
run_v2.bat
```

### الطريقة الثالثة:
```bash
# الطريقة التقليدية
python start_app.py
```

---

## 📋 المتطلبات

### البرامج المطلوبة:
- **Python 3.8+** (مستحسن 3.9+)
- **Windows 7+** (مستحسن Windows 10+)

### المكتبات المطلوبة:
```bash
pip install PySide6
pip install python-docx
pip install arabic-reshaper
pip install python-bidi
pip install Pillow
```

أو:
```bash
pip install -r requirements.txt
```

---

## 🎯 كيفية الاستخدام

### إدارة القوالب:
1. **من القائمة**: `أدوات > إدارة القوالب`
2. **اختصار**: `Ctrl+T`
3. **إضافة قالب جديد**: انقر "إضافة قالب جديد"
4. **استيراد قالب**: انقر "استيراد قالب" واختر ملف Word

### الإعدادات:
1. **من القائمة**: `أدوات > الإعدادات`
2. **اختصار**: `Ctrl+,`
3. **اختر التبويب المناسب** وعدّل الإعدادات
4. **انقر "تطبيق"** لحفظ التغييرات

### إنشاء خطاب:
1. **اختر مستأجر** من القائمة
2. **انقر "إنشاء خطاب"**
3. **اختر القالب المناسب**
4. **املأ البيانات المطلوبة**
5. **انقر "إنشاء"** لتوليد الخطاب

---

## 📁 هيكل المشروع

```
Desktop-as3am-7tab/
├── 📁 ui/                     # واجهات المستخدم
│   ├── main_window.py         # النافذة الرئيسية
│   ├── template_dialog.py     # 🆕 إدارة القوالب
│   ├── settings_dialog.py     # 🆕 الإعدادات
│   ├── tenant_dialog.py       # إدارة المستأجرين
│   └── letter_dialog.py       # إنشاء الخطابات
├── 📁 database/               # قاعدة البيانات
├── 📁 utils/                  # أدوات مساعدة
├── 📁 services/               # خدمات التطبيق
├── 📁 templates/              # قوالب الخطابات
├── 📁 output/                 # الخطابات المنشأة
├── 📁 config/                 # ملفات الإعدادات
├── 📁 logs/                   # ملفات السجلات
├── run_v2.py                  # 🆕 تشغيل محسن
├── update_app.py              # 🆕 أداة التحديث
└── README_v2.md               # 🆕 هذا الملف
```

---

## 🔧 استكشاف الأخطاء

### المشكلة: النافذة لا تظهر
**الحل**:
```bash
python run_v2.py
```

### المشكلة: مكتبات مفقودة
**الحل**:
```bash
pip install -r requirements.txt
```

### المشكلة: خطأ في القوالب
**الحل**:
```bash
python update_app.py
```

### المشكلة: مشاكل في العربية
**الحل**: 
- تأكد من تثبيت خط Tahoma
- استخدم الإعدادات لتغيير الخط

---

## 📊 مقارنة الإصدارات

| الميزة | الإصدار 1.0 | الإصدار 2.0 |
|--------|-------------|-------------|
| إدارة القوالب | ❌ محدودة | ✅ متقدمة |
| الإعدادات | ❌ ثابتة | ✅ قابلة للتخصيص |
| دعم العربية | ⚠️ أساسي | ✅ محسن |
| الواجهة | ⚠️ بسيطة | ✅ احترافية |
| الأداء | ⚠️ عادي | ✅ محسن |

---

## 🎯 نصائح للاستخدام الأمثل

### للمبتدئين:
1. **ابدأ بتشغيل** `update_app.py`
2. **استخدم** `run_v2.py` للتشغيل
3. **جرب الإعدادات** لتخصيص التطبيق
4. **أنشئ قوالب مخصصة** لاحتياجاتك

### للمستخدمين المتقدمين:
1. **خصص الإعدادات المتقدمة** في التبويب الرابع
2. **استخدم اختصارات لوحة المفاتيح** للسرعة
3. **أنشئ نسخ احتياطية** من القوالب المهمة
4. **راقب ملفات السجلات** لتتبع الأداء

---

## 🔮 التطوير المستقبلي

### الإصدار 2.1 (قريباً):
- [ ] تصدير التقارير المفصلة
- [ ] نظام النسخ الاحتياطية التلقائية
- [ ] دعم قوالب متعددة اللغات
- [ ] تحسينات الأداء

### الإصدار 2.2:
- [ ] إرسال عبر البريد الإلكتروني
- [ ] طباعة مباشرة للخطابات
- [ ] نظام المستخدمين والصلاحيات
- [ ] واجهة ويب اختيارية

---

## 📞 الدعم والمساعدة

### الملفات المفيدة:
- **`استكشاف_الأخطاء.md`** - دليل حل المشاكل
- **`تحديثات_الإصدار_2.0.md`** - تفاصيل التحديثات
- **`دليل_المستخدم_v2.md`** - دليل الاستخدام المفصل

### ملفات السجلات:
- **`logs/app_v2.log`** - سجل التطبيق المحسن
- **`logs/startup.log`** - سجل بدء التشغيل

---

## 🏆 شكر وتقدير

تم تطوير هذا الإصدار بعناية فائقة لتوفير أفضل تجربة مستخدم ممكنة.

**المطور**: Desktop as3am 7tab  
**تاريخ الإصدار**: مايو 2025  
**الإصدار**: 2.0.0  

---

## 🎊 استمتع بالتطبيق الجديد!

**نظام إدارة خطابات المستأجرين الآن أقوى وأكثر احترافية من أي وقت مضى!**

للبدء فوراً:
```bash
python run_v2.py
```

**مع تحيات فريق التطوير** 💙
