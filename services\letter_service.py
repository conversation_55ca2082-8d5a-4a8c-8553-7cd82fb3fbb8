# -*- coding: utf-8 -*-
"""
خدمة إنشاء الخطابات
Letter Creation Service
"""

import os
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn

from database.models import Tenant, LetterTemplate, Letter
from utils.arabic_support import ArabicSupport
from utils.file_utils import FileUtils
from config.settings import OUTPUT_DIR, TEMPLATES_DIR, TEMPLATE_VARIABLES


class LetterService:
    """خدمة إنشاء الخطابات"""
    
    def __init__(self):
        """تهيئة خدمة الخطابات"""
        self.output_dir = OUTPUT_DIR
        self.templates_dir = TEMPLATES_DIR
        
        # التأكد من وجود المجلدات
        FileUtils.ensure_directory_exists(str(self.output_dir))
        FileUtils.ensure_directory_exists(str(self.templates_dir))
    
    def create_letter_from_template(
        self, 
        tenant: Tenant, 
        template: Optional[LetterTemplate], 
        content: str,
        letter_number: str
    ) -> Dict[str, Any]:
        """
        إنشاء خطاب من قالب
        
        Args:
            tenant: بيانات المستأجر
            template: قالب الخطاب (اختياري)
            content: محتوى الخطاب
            letter_number: رقم الخطاب
            
        Returns:
            dict: معلومات الخطاب المُنشأ
        """
        try:
            result = {
                'success': False,
                'docx_path': None,
                'pdf_path': None,
                'error': None
            }
            
            # إنشاء متغيرات الاستبدال
            variables = self._prepare_variables(tenant, content, letter_number)
            
            # إنشاء المستند
            if template and os.path.exists(template.file_path):
                # استخدام القالب الموجود
                doc = Document(template.file_path)
                self._replace_variables_in_document(doc, variables)
            else:
                # إنشاء قالب افتراضي
                doc = self._create_default_template(variables)
            
            # إعداد الخط العربي
            self._setup_arabic_font(doc)
            
            # حفظ ملف Word
            docx_filename = self._generate_filename(tenant, letter_number, '.docx')
            docx_path = self.output_dir / docx_filename
            doc.save(str(docx_path))
            
            result['success'] = True
            result['docx_path'] = str(docx_path)
            
            logging.info(f"تم إنشاء الخطاب بنجاح: {docx_path}")
            
            return result
            
        except Exception as e:
            logging.error(f"خطأ في إنشاء الخطاب: {e}")
            result['error'] = str(e)
            return result
    
    def _prepare_variables(self, tenant: Tenant, content: str, letter_number: str) -> Dict[str, str]:
        """إعداد متغيرات الاستبدال"""
        current_date = datetime.now()
        
        variables = {
            '{Name}': ArabicSupport.reshape_arabic_text(tenant.name),
            '{Unit}': tenant.unit_number,
            '{Building}': tenant.building_number,
            '{LetterNo}': letter_number,
            '{Date}': ArabicSupport.format_arabic_date(current_date),
            '{Content}': ArabicSupport.reshape_arabic_text(content)
        }
        
        return variables
    
    def _replace_variables_in_document(self, doc: Document, variables: Dict[str, str]):
        """استبدال المتغيرات في المستند"""
        try:
            # استبدال في الفقرات
            for paragraph in doc.paragraphs:
                for variable, value in variables.items():
                    if variable in paragraph.text:
                        paragraph.text = paragraph.text.replace(variable, value)
                        # إعداد الاتجاه من اليمين لليسار
                        paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            
            # استبدال في الجداول
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for variable, value in variables.items():
                            if variable in cell.text:
                                cell.text = cell.text.replace(variable, value)
            
            # استبدال في الرؤوس والتذييلات
            for section in doc.sections:
                # الرأس
                if section.header:
                    for paragraph in section.header.paragraphs:
                        for variable, value in variables.items():
                            if variable in paragraph.text:
                                paragraph.text = paragraph.text.replace(variable, value)
                
                # التذييل
                if section.footer:
                    for paragraph in section.footer.paragraphs:
                        for variable, value in variables.items():
                            if variable in paragraph.text:
                                paragraph.text = paragraph.text.replace(variable, value)
                                
        except Exception as e:
            logging.error(f"خطأ في استبدال المتغيرات: {e}")
    
    def _create_default_template(self, variables: Dict[str, str]) -> Document:
        """إنشاء قالب افتراضي"""
        doc = Document()
        
        # إعداد الصفحة
        section = doc.sections[0]
        section.page_height = Inches(11.69)  # A4
        section.page_width = Inches(8.27)
        section.left_margin = Inches(1)
        section.right_margin = Inches(1)
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        
        # العنوان
        title = doc.add_heading('خطاب إداري', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # معلومات الخطاب
        info_paragraph = doc.add_paragraph()
        info_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        info_paragraph.add_run(f"رقم الخطاب: {variables['{LetterNo}']}\n")
        info_paragraph.add_run(f"التاريخ: {variables['{Date}']}\n")
        
        # فاصل
        doc.add_paragraph("=" * 50).alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # بيانات المستأجر
        tenant_info = doc.add_paragraph()
        tenant_info.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        tenant_info.add_run("إلى المحترم/ة: ").bold = True
        tenant_info.add_run(f"{variables['{Name}']}\n")
        tenant_info.add_run(f"رقم الوحدة: {variables['{Unit}']}\n")
        tenant_info.add_run(f"رقم العمارة: {variables['{Building}']}\n")
        
        # التحية
        greeting = doc.add_paragraph("السلام عليكم ورحمة الله وبركاته،")
        greeting.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        
        # المحتوى
        content_paragraph = doc.add_paragraph()
        content_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        content_paragraph.add_run(variables['{Content}'])
        
        # الختام
        closing = doc.add_paragraph()
        closing.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        closing.add_run("وتفضلوا بقبول فائق الاحترام والتقدير،\n\n")
        closing.add_run("إدارة العقار")
        
        return doc
    
    def _setup_arabic_font(self, doc: Document):
        """إعداد الخط العربي للمستند"""
        try:
            # إعداد الخط الافتراضي
            style = doc.styles['Normal']
            font = style.font
            font.name = 'Tahoma'
            font.size = Inches(0.12)  # 12pt
            
            # إعداد الخط للنصوص المعقدة (العربية)
            r = style.element
            r.rPr.rFonts.set(qn('w:cs'), 'Tahoma')
            
        except Exception as e:
            logging.error(f"خطأ في إعداد الخط العربي: {e}")
    
    def _generate_filename(self, tenant: Tenant, letter_number: str, extension: str) -> str:
        """توليد اسم ملف فريد"""
        try:
            # تنظيف اسم المستأجر
            clean_name = FileUtils.sanitize_filename(tenant.name)
            
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d")
            filename = f"{letter_number}_{clean_name}_{timestamp}{extension}"
            
            # التأكد من عدم وجود الملف
            return FileUtils.get_unique_filename(
                str(self.output_dir), 
                filename.replace(extension, ''), 
                extension
            )
            
        except Exception as e:
            logging.error(f"خطأ في توليد اسم الملف: {e}")
            return f"خطاب_{datetime.now().strftime('%Y%m%d_%H%M%S')}{extension}"
    
    def convert_to_pdf(self, docx_path: str) -> Optional[str]:
        """تحويل ملف Word إلى PDF"""
        try:
            from docx2pdf import convert
            
            pdf_path = docx_path.replace('.docx', '.pdf')
            convert(docx_path, pdf_path)
            
            if os.path.exists(pdf_path):
                logging.info(f"تم تحويل الملف إلى PDF: {pdf_path}")
                return pdf_path
            else:
                logging.error("فشل في إنشاء ملف PDF")
                return None
                
        except ImportError:
            logging.error("مكتبة docx2pdf غير مثبتة")
            return None
        except Exception as e:
            logging.error(f"خطأ في تحويل الملف إلى PDF: {e}")
            return None
    
    def validate_template(self, template_path: str) -> Dict[str, Any]:
        """التحقق من صحة القالب"""
        result = {
            'valid': False,
            'variables_found': [],
            'missing_variables': [],
            'error': None
        }
        
        try:
            if not os.path.exists(template_path):
                result['error'] = "ملف القالب غير موجود"
                return result
            
            # قراءة القالب
            doc = Document(template_path)
            
            # البحث عن المتغيرات
            text_content = ""
            for paragraph in doc.paragraphs:
                text_content += paragraph.text + " "
            
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text_content += cell.text + " "
            
            # التحقق من وجود المتغيرات
            found_variables = []
            for variable in TEMPLATE_VARIABLES.keys():
                if variable in text_content:
                    found_variables.append(variable)
            
            result['valid'] = True
            result['variables_found'] = found_variables
            result['missing_variables'] = [
                var for var in TEMPLATE_VARIABLES.keys() 
                if var not in found_variables
            ]
            
        except Exception as e:
            result['error'] = str(e)
        
        return result
