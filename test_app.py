# -*- coding: utf-8 -*-
"""
اختبار سريع للتطبيق
Quick Application Test
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """اختبار استيراد الوحدات"""
    print("اختبار استيراد الوحدات...")
    
    try:
        from config.settings import APP_NAME
        print(f"✓ تم استيراد الإعدادات: {APP_NAME}")
    except Exception as e:
        print(f"✗ خطأ في استيراد الإعدادات: {e}")
        return False
    
    try:
        from database.database_manager import DatabaseManager
        print("✓ تم استيراد مدير قاعدة البيانات")
    except Exception as e:
        print(f"✗ خطأ في استيراد مدير قاعدة البيانات: {e}")
        return False
    
    try:
        from utils.arabic_support import ArabicSupport
        print("✓ تم استيراد دعم اللغة العربية")
    except Exception as e:
        print(f"✗ خطأ في استيراد دعم اللغة العربية: {e}")
        return False
    
    try:
        from services.letter_service import LetterService
        print("✓ تم استيراد خدمة الخطابات")
    except Exception as e:
        print(f"✗ خطأ في استيراد خدمة الخطابات: {e}")
        return False
    
    return True

def test_database():
    """اختبار قاعدة البيانات"""
    print("\nاختبار قاعدة البيانات...")
    
    try:
        from database.database_manager import DatabaseManager
        from database.models import Tenant
        
        db = DatabaseManager()
        print("✓ تم إنشاء مدير قاعدة البيانات")
        
        # اختبار إضافة مستأجر
        tenant = Tenant(
            name="مستأجر تجريبي",
            unit_number="101",
            building_number="A",
            phone_number="0501234567"
        )
        
        tenant_id = db.add_tenant(tenant)
        if tenant_id > 0:
            print(f"✓ تم إضافة مستأجر تجريبي بالمعرف: {tenant_id}")
            
            # اختبار جلب المستأجر
            retrieved_tenant = db.get_tenant_by_id(tenant_id)
            if retrieved_tenant:
                print(f"✓ تم جلب المستأجر: {retrieved_tenant.name}")
                
                # حذف المستأجر التجريبي
                if db.delete_tenant(tenant_id):
                    print("✓ تم حذف المستأجر التجريبي")
                else:
                    print("✗ فشل في حذف المستأجر التجريبي")
            else:
                print("✗ فشل في جلب المستأجر")
        else:
            print("✗ فشل في إضافة المستأجر")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_arabic_support():
    """اختبار دعم اللغة العربية"""
    print("\nاختبار دعم اللغة العربية...")
    
    try:
        from utils.arabic_support import ArabicSupport
        
        # اختبار إعادة تشكيل النص العربي
        arabic_text = "مرحباً بكم في نظام إدارة الخطابات"
        reshaped = ArabicSupport.reshape_arabic_text(arabic_text)
        print(f"✓ تم إعادة تشكيل النص العربي: {reshaped}")
        
        # اختبار تحويل الأرقام
        numbers = "12345"
        arabic_numbers = ArabicSupport.convert_numbers_to_arabic(numbers)
        print(f"✓ تم تحويل الأرقام إلى العربية: {arabic_numbers}")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في اختبار دعم اللغة العربية: {e}")
        return False

def test_letter_service():
    """اختبار خدمة الخطابات"""
    print("\nاختبار خدمة الخطابات...")
    
    try:
        from services.letter_service import LetterService
        from database.models import Tenant
        
        service = LetterService()
        print("✓ تم إنشاء خدمة الخطابات")
        
        # إنشاء مستأجر تجريبي
        tenant = Tenant(
            name="أحمد محمد",
            unit_number="202",
            building_number="B",
            phone_number="0501234567"
        )
        
        # اختبار إنشاء خطاب
        result = service.create_letter_from_template(
            tenant=tenant,
            template=None,  # استخدام القالب الافتراضي
            content="هذا خطاب تجريبي للاختبار",
            letter_number="خطاب-0001"
        )
        
        if result['success']:
            print(f"✓ تم إنشاء الخطاب بنجاح: {result['docx_path']}")
            
            # حذف الملف التجريبي
            import os
            if os.path.exists(result['docx_path']):
                os.remove(result['docx_path'])
                print("✓ تم حذف الملف التجريبي")
        else:
            print(f"✗ فشل في إنشاء الخطاب: {result['error']}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في اختبار خدمة الخطابات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 50)
    print("اختبار نظام إدارة خطابات المستأجرين")
    print("=" * 50)
    
    tests = [
        ("استيراد الوحدات", test_imports),
        ("قاعدة البيانات", test_database),
        ("دعم اللغة العربية", test_arabic_support),
        ("خدمة الخطابات", test_letter_service)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✓ نجح اختبار {test_name}")
            else:
                print(f"✗ فشل اختبار {test_name}")
        except Exception as e:
            print(f"✗ خطأ في اختبار {test_name}: {e}")
    
    print(f"\n{'='*50}")
    print(f"نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("✓ جميع الاختبارات نجحت! البرنامج جاهز للتشغيل.")
        return 0
    else:
        print("✗ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
