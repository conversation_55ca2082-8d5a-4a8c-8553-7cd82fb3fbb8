# -*- coding: utf-8 -*-
"""
محسن واجهة المستخدم
UI Enhancer for Professional Look
"""

from PySide6.QtWidgets import QWidget, QPushButton, QTableWidget, QLineEdit, QTextEdit, QComboBox
from PySide6.QtCore import Qt
from utils.arabic_support import ArabicSupport

class UIEnhancer:
    """فئة تحسين واجهة المستخدم"""
    
    @staticmethod
    def enhance_widget(widget: QWidget):
        """تحسين عنصر واجهة المستخدم"""
        try:
            # تطبيق دعم RTL
            ArabicSupport.setup_rtl_layout(widget)
            ArabicSupport.setup_advanced_rtl(widget)
            ArabicSupport.setup_arabic_font(widget, 11)
            
            # تحسين الأزرار
            UIEnhancer._enhance_buttons(widget)
            
            # تحسين الجداول
            UIEnhancer._enhance_tables(widget)
            
            # تحسين حقول الإدخال
            UIEnhancer._enhance_inputs(widget)
            
        except Exception as e:
            print(f"خطأ في تحسين العنصر: {e}")
    
    @staticmethod
    def _enhance_buttons(widget: QWidget):
        """تحسين الأزرار"""
        buttons = widget.findChildren(QPushButton)
        
        for button in buttons:
            # تطبيق الأنماط حسب النص
            text = button.text().lower()
            
            if any(word in text for word in ['حفظ', 'إضافة', 'إنشاء', 'تأكيد', 'موافق']):
                button.setProperty("class", "success")
            elif any(word in text for word in ['حذف', 'إلغاء', 'رفض']):
                button.setProperty("class", "danger")
            elif any(word in text for word in ['تحذير', 'تنبيه']):
                button.setProperty("class", "warning")
            elif any(word in text for word in ['معلومات', 'تفاصيل', 'عرض']):
                button.setProperty("class", "info")
            elif any(word in text for word in ['تعديل', 'تحديث', 'تطبيق']):
                button.setProperty("class", "primary")
            
            # تطبيق الأنماط
            button.style().unpolish(button)
            button.style().polish(button)
    
    @staticmethod
    def _enhance_tables(widget: QWidget):
        """تحسين الجداول"""
        tables = widget.findChildren(QTableWidget)
        
        for table in tables:
            # تطبيق RTL
            table.setLayoutDirection(Qt.RightToLeft)
            
            # تحسين الرؤوس
            header = table.horizontalHeader()
            if header:
                header.setLayoutDirection(Qt.RightToLeft)
            
            # تحسين الصفوف المتناوبة
            table.setAlternatingRowColors(True)
            
            # تحسين التحديد
            table.setSelectionBehavior(QTableWidget.SelectRows)
    
    @staticmethod
    def _enhance_inputs(widget: QWidget):
        """تحسين حقول الإدخال"""
        # حقول النص
        line_edits = widget.findChildren(QLineEdit)
        for line_edit in line_edits:
            line_edit.setLayoutDirection(Qt.RightToLeft)
            line_edit.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        
        # مناطق النص
        text_edits = widget.findChildren(QTextEdit)
        for text_edit in text_edits:
            text_edit.setLayoutDirection(Qt.RightToLeft)
        
        # القوائم المنسدلة
        combo_boxes = widget.findChildren(QComboBox)
        for combo_box in combo_boxes:
            combo_box.setLayoutDirection(Qt.RightToLeft)
    
    @staticmethod
    def apply_button_style(button: QPushButton, style_type: str):
        """تطبيق نمط محدد على زر"""
        button.setProperty("class", style_type)
        button.style().unpolish(button)
        button.style().polish(button)
    
    @staticmethod
    def create_professional_button(text: str, style_type: str = "default") -> QPushButton:
        """إنشاء زر احترافي"""
        button = QPushButton(ArabicSupport.reshape_arabic_text(text))
        UIEnhancer.apply_button_style(button, style_type)
        return button
    
    @staticmethod
    def enhance_dialog(dialog):
        """تحسين نافذة حوار"""
        try:
            # تطبيق RTL
            dialog.setLayoutDirection(Qt.RightToLeft)
            
            # تحسين جميع العناصر
            UIEnhancer.enhance_widget(dialog)
            
            # تحسين العنوان
            if hasattr(dialog, 'setWindowTitle'):
                title = dialog.windowTitle()
                if title:
                    dialog.setWindowTitle(ArabicSupport.reshape_arabic_text(title))
            
        except Exception as e:
            print(f"خطأ في تحسين النافذة: {e}")
    
    @staticmethod
    def enhance_main_window(window):
        """تحسين النافذة الرئيسية"""
        try:
            # تطبيق RTL
            window.setLayoutDirection(Qt.RightToLeft)
            
            # تحسين شريط القوائم
            menu_bar = window.menuBar()
            if menu_bar:
                menu_bar.setLayoutDirection(Qt.RightToLeft)
            
            # تحسين شريط الأدوات
            toolbars = window.findChildren(window.toolBar().__class__)
            for toolbar in toolbars:
                toolbar.setLayoutDirection(Qt.RightToLeft)
            
            # تحسين شريط الحالة
            status_bar = window.statusBar()
            if status_bar:
                status_bar.setLayoutDirection(Qt.RightToLeft)
            
            # تحسين جميع العناصر
            UIEnhancer.enhance_widget(window)
            
        except Exception as e:
            print(f"خطأ في تحسين النافذة الرئيسية: {e}")
    
    @staticmethod
    def setup_rtl_table(table: QTableWidget, headers: list):
        """إعداد جدول مع دعم RTL كامل"""
        try:
            # تطبيق RTL
            table.setLayoutDirection(Qt.RightToLeft)
            
            # إعداد الرؤوس
            table.setHorizontalHeaderLabels([
                ArabicSupport.reshape_arabic_text(header) for header in headers
            ])
            
            # إعداد الرأس الأفقي
            header = table.horizontalHeader()
            header.setLayoutDirection(Qt.RightToLeft)
            
            # تحسين المظهر
            table.setAlternatingRowColors(True)
            table.setSelectionBehavior(QTableWidget.SelectRows)
            
        except Exception as e:
            print(f"خطأ في إعداد الجدول: {e}")
    
    @staticmethod
    def add_table_row(table: QTableWidget, row_data: list):
        """إضافة صف للجدول مع دعم RTL"""
        try:
            from PySide6.QtWidgets import QTableWidgetItem
            
            row = table.rowCount()
            table.insertRow(row)
            
            for col, data in enumerate(row_data):
                # تشكيل النص العربي
                text = ArabicSupport.reshape_arabic_text(str(data))
                item = QTableWidgetItem(text)
                
                # تطبيق RTL على العنصر
                item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                
                table.setItem(row, col, item)
                
        except Exception as e:
            print(f"خطأ في إضافة صف للجدول: {e}")
    
    @staticmethod
    def create_rtl_layout(layout_type="vertical"):
        """إنشاء تخطيط مع دعم RTL"""
        try:
            from PySide6.QtWidgets import QVBoxLayout, QHBoxLayout
            
            if layout_type == "vertical":
                layout = QVBoxLayout()
            else:
                layout = QHBoxLayout()
            
            layout.setLayoutDirection(Qt.RightToLeft)
            return layout
            
        except Exception as e:
            print(f"خطأ في إنشاء التخطيط: {e}")
            return None
