# -*- coding: utf-8 -*-
"""
اختبار سريع لإصلاح النصوص العربية
Quick Test for Arabic Text Fix
"""

import sys
from pathlib import Path

# إعداد المسارات
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """اختبار سريع للإصلاح"""
    print("🔧 اختبار إصلاح النصوص العربية")
    print("=" * 40)
    
    try:
        # استيراد المكتبات
        from PySide6.QtWidgets import QApplication
        from utils.arabic_support import ArabicSupport
        from ui.main_window import MainWindow
        
        print("✅ تم استيراد المكتبات بنجاح")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد RTL
        ArabicSupport.setup_application_rtl(app)
        print("✅ تم إعداد RTL للتطبيق")
        
        # إنشاء النافذة
        window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # عرض النافذة
        window.show()
        window.raise_()
        window.activateWindow()
        
        print("🎉 تم تشغيل التطبيق بنجاح!")
        print("📋 تحقق من الجدول - يجب أن تظهر النصوص العربية بشكل صحيح")
        print("🔤 النصوص يجب أن تكون من اليمين لليسار")
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    
    if exit_code == 0:
        print("✅ تم الإغلاق بنجاح")
    else:
        print("❌ حدث خطأ")
    
    sys.exit(exit_code)
