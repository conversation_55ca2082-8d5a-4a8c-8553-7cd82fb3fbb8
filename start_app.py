# -*- coding: utf-8 -*-
"""
ملف تشغيل محسن لنظام إدارة خطابات المستأجرين
Enhanced Launcher for Tenant Letters Management System
"""

import sys
import os
import time
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        print(f"   الإصدار الحالي: {sys.version}")
        return False
    return True

def install_requirements():
    """تثبيت المتطلبات إذا لم تكن مثبتة"""
    required_modules = ['PySide6', 'python-docx', 'arabic-reshaper', 'python-bidi']
    missing = []
    
    for module in required_modules:
        try:
            if module == 'python-docx':
                __import__('docx')
            elif module == 'arabic-reshaper':
                __import__('arabic_reshaper')
            elif module == 'python-bidi':
                __import__('bidi')
            else:
                __import__(module)
        except ImportError:
            missing.append(module)
    
    if missing:
        print(f"📦 تثبيت المكتبات المفقودة: {', '.join(missing)}")
        try:
            import subprocess
            for module in missing:
                print(f"   تثبيت {module}...")
                result = subprocess.run([sys.executable, '-m', 'pip', 'install', module], 
                                      capture_output=True, text=True)
                if result.returncode != 0:
                    print(f"   ❌ فشل في تثبيت {module}")
                    return False
                else:
                    print(f"   ✅ تم تثبيت {module}")
        except Exception as e:
            print(f"❌ خطأ في التثبيت: {e}")
            return False
    
    return True

def create_log_file():
    """إنشاء ملف سجل للتشخيص"""
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)
    
    log_file = log_dir / "startup.log"
    return str(log_file)

def main():
    """الدالة الرئيسية"""
    print("🚀 نظام إدارة خطابات المستأجرين")
    print("=" * 50)
    
    # إنشاء ملف السجل
    log_file = create_log_file()
    
    try:
        # التحقق من Python
        if not check_python_version():
            input("اضغط Enter للخروج...")
            return 1
        
        print("✅ إصدار Python مناسب")
        
        # تثبيت المتطلبات
        if not install_requirements():
            print("❌ فشل في تثبيت المتطلبات")
            input("اضغط Enter للخروج...")
            return 1
        
        print("✅ جميع المتطلبات متوفرة")
        
        # تشغيل التطبيق
        print("🏠 تشغيل التطبيق الرئيسي...")
        
        # استيراد وتشغيل التطبيق
        from PySide6.QtWidgets import QApplication, QMessageBox
        from PySide6.QtCore import Qt
        from PySide6.QtGui import QFont
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setQuitOnLastWindowClosed(True)
        
        # إعداد الخط والاتجاه
        font = QFont("Tahoma", 10)
        app.setFont(font)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد النظام
        from config.settings import create_directories
        from utils.template_creator import TemplateCreator
        
        create_directories()
        TemplateCreator.create_all_default_templates()
        
        # إنشاء النافذة الرئيسية
        from ui.main_window import MainWindow
        
        main_window = MainWindow()
        
        # التأكد من ظهور النافذة
        main_window.show()
        main_window.raise_()  # رفع النافذة للمقدمة
        main_window.activateWindow()  # تفعيل النافذة
        
        print("✅ تم تشغيل التطبيق بنجاح!")
        print("📝 إذا لم تظهر النافذة، تحقق من شريط المهام")
        
        # تشغيل التطبيق
        return app.exec()
        
    except ImportError as e:
        error_msg = f"خطأ في استيراد المكتبات: {e}"
        print(f"❌ {error_msg}")
        
        # كتابة الخطأ في ملف السجل
        try:
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"خطأ في الاستيراد: {e}\n")
                f.write(f"Python version: {sys.version}\n")
                f.write(f"Python path: {sys.path}\n")
        except:
            pass
        
        print(f"💡 تم حفظ تفاصيل الخطأ في: {log_file}")
        input("اضغط Enter للخروج...")
        return 1
        
    except Exception as e:
        error_msg = f"خطأ في تشغيل التطبيق: {e}"
        print(f"❌ {error_msg}")
        
        # كتابة الخطأ في ملف السجل
        try:
            import traceback
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"خطأ في التشغيل: {e}\n")
                f.write(f"Traceback:\n{traceback.format_exc()}\n")
        except:
            pass
        
        # محاولة إظهار رسالة خطأ رسومية
        try:
            if 'app' in locals():
                QMessageBox.critical(
                    None, "خطأ في التطبيق",
                    f"حدث خطأ أثناء تشغيل التطبيق:\n\n{e}\n\n"
                    f"تم حفظ تفاصيل الخطأ في:\n{log_file}"
                )
        except:
            print(f"💡 تم حفظ تفاصيل الخطأ في: {log_file}")
        
        input("اضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
