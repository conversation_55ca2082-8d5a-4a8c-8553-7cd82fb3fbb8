@echo off
chcp 65001 > nul
title نظام إدارة خطابات المستأجرين

echo ========================================
echo    نظام إدارة خطابات المستأجرين
echo    Tenant Letters Management System
echo ========================================
echo.

echo جاري التحقق من Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من python.org
    pause
    exit /b 1
)

echo جاري التحقق من المكتبات المطلوبة...
pip show PySide6 > nul 2>&1
if errorlevel 1 (
    echo جاري تثبيت المكتبات المطلوبة...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo خطأ في تثبيت المكتبات
        pause
        exit /b 1
    )
)

echo جاري تشغيل البرنامج...
echo.
python main.py

if errorlevel 1 (
    echo.
    echo حدث خطأ أثناء تشغيل البرنامج
    echo يرجى مراجعة ملف السجل في مجلد logs
    pause
)
