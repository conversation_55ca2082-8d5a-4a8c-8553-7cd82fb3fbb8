# -*- coding: utf-8 -*-
"""
نظام إدارة خطابات المستأجرين - نسخة مبسطة
Tenant Letters Management System - Simple Version
"""

import sys
import logging
from pathlib import Path

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """إعداد نظام السجلات"""
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "app.log", encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_dependencies():
    """التحقق من المتطلبات المطلوبة"""
    required_modules = ['PySide6', 'docx', 'arabic_reshaper', 'bidi']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("❌ المكتبات التالية مطلوبة ولكنها غير مثبتة:")
        for module in missing_modules:
            print(f"   - {module}")
        print(f"\n💡 لتثبيتها، شغّل الأمر التالي:")
        print(f"   pip install {' '.join(missing_modules)}")
        return False
    
    return True

def main():
    """الدالة الرئيسية"""
    try:
        print("🚀 بدء تشغيل نظام إدارة خطابات المستأجرين...")
        
        # إعداد السجلات
        setup_logging()
        logging.info("بدء تشغيل التطبيق")
        
        # التحقق من المتطلبات
        if not check_dependencies():
            input("\nاضغط Enter للخروج...")
            return 1
        
        # استيراد المكتبات المطلوبة
        from PySide6.QtWidgets import QApplication, QMessageBox
        from PySide6.QtCore import Qt
        from PySide6.QtGui import QFont
        
        from config.settings import APP_NAME, APP_VERSION, create_directories
        from ui.main_window import MainWindow
        from utils.template_creator import TemplateCreator
        
        print("✅ تم تحميل جميع المكتبات بنجاح")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد التطبيق
        app.setApplicationName(APP_NAME)
        app.setApplicationVersion(APP_VERSION)
        app.setOrganizationName("Desktop as3am 7tab")
        
        # إعداد الخط الافتراضي للتطبيق
        font = QFont("Tahoma", 10)
        app.setFont(font)
        
        # إعداد اتجاه التطبيق (RTL)
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("⚙️ جاري إعداد النظام...")
        
        # إنشاء المجلدات المطلوبة
        create_directories()
        
        # إنشاء القوالب الافتراضية إذا لم تكن موجودة
        TemplateCreator.create_all_default_templates()
        
        print("🏠 جاري إنشاء النافذة الرئيسية...")
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        main_window.show()
        
        print("✅ تم تشغيل التطبيق بنجاح!")
        print("📝 إذا لم تظهر النافذة، تحقق من شريط المهام")
        
        logging.info("تم تشغيل التطبيق بنجاح")
        
        # تشغيل التطبيق
        return app.exec()
        
    except ImportError as e:
        error_msg = f"خطأ في استيراد المكتبات: {e}"
        print(f"❌ {error_msg}")
        logging.error(error_msg)
        input("\nاضغط Enter للخروج...")
        return 1
        
    except Exception as e:
        error_msg = f"خطأ في تشغيل التطبيق: {e}"
        print(f"❌ {error_msg}")
        logging.error(error_msg)
        
        # محاولة إظهار رسالة خطأ رسومية
        try:
            from PySide6.QtWidgets import QApplication, QMessageBox
            if 'app' not in locals():
                app = QApplication(sys.argv)
            QMessageBox.critical(
                None, "خطأ في التطبيق",
                f"حدث خطأ أثناء تشغيل التطبيق:\n\n{e}\n\n"
                "يرجى التحقق من ملف السجل للمزيد من التفاصيل."
            )
        except:
            print("💡 يرجى التحقق من ملف logs/app.log للمزيد من التفاصيل")
        
        input("\nاضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
