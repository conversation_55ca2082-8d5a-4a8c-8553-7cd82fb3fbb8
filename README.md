# نظام إدارة خطابات المستأجرين
## Tenant Letters Management System

نظام شامل لإدارة خطابات المستأجرين مع دعم كامل للغة العربية وإمكانية إرسال الخطابات عبر واتساب.

## المميزات الرئيسية

### ✅ إدارة المستأجرين
- إضافة وتعديل وحذف بيانات المستأجرين
- البحث والتصفية في قائمة المستأجرين
- حفظ معلومات الاتصال (الهاتف والبريد الإلكتروني)

### ✅ إنشاء الخطابات
- إنشاء خطابات من قوالب Word قابلة للتخصيص
- دعم المتغيرات التلقائية (الاسم، رقم الوحدة، التاريخ، إلخ)
- توليد أرقام خطابات فريدة تلقائياً
- تصدير الخطابات بصيغة Word و PDF

### ✅ إرسال واتساب
- فتح واتساب ويب تلقائياً مع الخطاب
- دعم أرقام الهواتف السعودية
- رسائل مخصصة للمستأجرين

### ✅ دعم اللغة العربية
- واجهة مستخدم باللغة العربية مع اتجاه RTL
- دعم الخطوط العربية
- تنسيق التواريخ بالعربية

## المتطلبات التقنية

### Python 3.8+
### المكتبات المطلوبة:
```
PySide6>=6.4.0
python-docx>=0.8.11
docx2pdf>=0.1.8
arabic-reshaper>=2.1.4
python-bidi>=0.4.2
Pillow>=9.0.0
requests>=2.28.0
```

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd Desktop-as3am-7tab
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل البرنامج
```bash
python main.py
```

## بنية المشروع

```
Desktop-as3am-7tab/
├── main.py                    # نقطة البداية الرئيسية
├── requirements.txt           # المتطلبات والمكتبات
├── config/
│   └── settings.py           # إعدادات التطبيق
├── database/
│   ├── models.py             # نماذج قاعدة البيانات
│   └── database_manager.py   # إدارة قاعدة البيانات
├── ui/
│   ├── main_window.py        # النافذة الرئيسية
│   ├── tenant_dialog.py      # نافذة إدارة المستأجرين
│   └── letter_dialog.py      # نافذة إنشاء الخطابات
├── services/
│   ├── letter_service.py     # خدمة إنشاء الخطابات
│   └── whatsapp_service.py   # خدمة إرسال واتساب
├── utils/
│   ├── arabic_support.py     # دعم اللغة العربية
│   └── file_utils.py         # أدوات التعامل مع الملفات
├── templates/               # مجلد القوالب
├── output/                 # مجلد الخطابات المُنشأة
└── data/                   # مجلد قاعدة البيانات
```

## كيفية الاستخدام

### 1. إضافة مستأجر جديد
- انقر على "إضافة مستأجر" في الشريط العلوي
- املأ البيانات المطلوبة (الاسم، رقم الوحدة، رقم العمارة)
- أضف معلومات الاتصال (اختياري)
- انقر "حفظ"

### 2. إنشاء خطاب
- انقر على "إنشاء خطاب" في الشريط العلوي
- اختر المستأجر من القائمة
- اختر القالب (أو استخدم القالب الافتراضي)
- اكتب محتوى الخطاب
- حدد خيارات الإنشاء (PDF، واتساب)
- انقر "إنشاء الخطاب"

### 3. إرسال عبر واتساب
- عند إنشاء الخطاب، فعّل خيار "فتح واتساب للإرسال"
- سيتم فتح واتساب ويب تلقائياً مع رسالة جاهزة
- أرفق ملف الخطاب وأرسل

## المتغيرات المدعومة في القوالب

عند إنشاء قوالب Word، يمكنك استخدام المتغيرات التالية:

- `{Name}` - اسم المستأجر
- `{Unit}` - رقم الوحدة
- `{Building}` - رقم العمارة
- `{LetterNo}` - رقم الخطاب
- `{Date}` - تاريخ الخطاب
- `{Content}` - محتوى الخطاب

## الملفات المُنشأة

- **الخطابات**: تُحفظ في مجلد `output/`
- **قاعدة البيانات**: تُحفظ في مجلد `data/tenants.db`
- **القوالب**: تُحفظ في مجلد `templates/`
- **السجلات**: تُحفظ في مجلد `logs/`

## المتوافقية

- **نظام التشغيل**: Windows 7 فما فوق
- **Python**: 3.8 فما فوق
- **الخطوط**: يدعم الخطوط العربية (Tahoma, Arial Unicode MS)

## استكشاف الأخطاء

### مشكلة في تثبيت المكتبات
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

### مشكلة في تحويل PDF
تأكد من تثبيت Microsoft Word أو LibreOffice

### مشكلة في عرض النصوص العربية
تأكد من وجود خط Tahoma في النظام

## التطوير المستقبلي

- [ ] إدارة القوالب من داخل البرنامج
- [ ] تقارير مفصلة وإحصائيات
- [ ] نسخ احتياطية تلقائية
- [ ] دعم قوالب متعددة اللغات
- [ ] تكامل مع أنظمة إدارة العقارات

## الدعم والمساهمة

للإبلاغ عن مشاكل أو اقتراح تحسينات، يرجى إنشاء Issue في المستودع.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

---

**تم التطوير بواسطة**: Desktop as3am 7tab  
**الإصدار**: 1.0.0  
**تاريخ الإصدار**: 2025
