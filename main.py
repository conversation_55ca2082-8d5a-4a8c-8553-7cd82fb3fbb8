# -*- coding: utf-8 -*-
"""
نظام إدارة خطابات المستأجرين
Tenant Letters Management System

نقطة البداية الرئيسية للتطبيق
Main Entry Point
"""

import sys
import logging
from pathlib import Path

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

from config.settings import APP_NAME, APP_VERSION, create_directories
from ui.main_window import MainWindow
from utils.template_creator import TemplateCreator


def setup_logging():
    """إعداد نظام السجلات"""
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "app.log", encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def check_dependencies():
    """التحقق من المتطلبات المطلوبة"""
    required_modules = [
        'PySide6',
        'docx',
        'arabic_reshaper',
        'bidi'
    ]

    missing_modules = []

    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)

    if missing_modules:
        error_msg = (
            "المكتبات التالية مطلوبة ولكنها غير مثبتة:\n\n" +
            "\n".join(f"- {module}" for module in missing_modules) +
            "\n\nيرجى تثبيتها باستخدام الأمر:\n" +
            f"pip install {' '.join(missing_modules)}"
        )

        QApplication(sys.argv)
        QMessageBox.critical(None, "مكتبات مفقودة", error_msg)
        return False

    return True


def main():
    """الدالة الرئيسية"""
    try:
        # إعداد السجلات
        setup_logging()
        logging.info("بدء تشغيل التطبيق")

        # التحقق من المتطلبات
        if not check_dependencies():
            return 1

        # إنشاء التطبيق
        app = QApplication(sys.argv)

        # إعداد التطبيق
        app.setApplicationName(APP_NAME)
        app.setApplicationVersion(APP_VERSION)
        app.setOrganizationName("Desktop as3am 7tab")

        # إعداد الخط الافتراضي للتطبيق
        font = QFont("Tahoma", 10)
        app.setFont(font)

        # إعداد اتجاه التطبيق (RTL)
        app.setLayoutDirection(Qt.RightToLeft)

        # إنشاء المجلدات المطلوبة
        create_directories()

        # إنشاء القوالب الافتراضية إذا لم تكن موجودة
        TemplateCreator.create_all_default_templates()

        # إنشاء النافذة الرئيسية مباشرة (بدون شاشة بداية)
        main_window = MainWindow()
        main_window.show()

        logging.info("تم تشغيل التطبيق بنجاح")

        # تشغيل التطبيق
        return app.exec()

    except Exception as e:
        logging.error(f"خطأ في تشغيل التطبيق: {e}")

        # إظهار رسالة خطأ للمستخدم
        try:
            app = QApplication(sys.argv)
            QMessageBox.critical(
                None, "خطأ في التطبيق",
                f"حدث خطأ أثناء تشغيل التطبيق:\n\n{e}\n\n"
                "يرجى التحقق من ملف السجل للمزيد من التفاصيل."
            )
        except:
            print(f"خطأ في التطبيق: {e}")

        return 1


if __name__ == "__main__":
    sys.exit(main())
