# -*- coding: utf-8 -*-
"""
تشغيل التطبيق - الإصدار 2.0
Enhanced Application Launcher v2.0
"""

import sys
import os
import logging
from pathlib import Path

# إعداد المسارات
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_enhanced_logging():
    """إعداد نظام السجلات المحسن"""
    try:
        logs_dir = project_root / "logs"
        logs_dir.mkdir(exist_ok=True)
        
        # إعداد السجل الرئيسي
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(logs_dir / "app_v2.log", encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        logger = logging.getLogger(__name__)
        logger.info("🚀 بدء تشغيل التطبيق - الإصدار 2.0")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد السجلات: {e}")
        return False

def check_requirements():
    """فحص المتطلبات والمكتبات"""
    print("🔍 فحص المتطلبات...")
    
    required_modules = [
        ('PySide6', 'PySide6.QtWidgets'),
        ('python-docx', 'docx'),
        ('arabic-reshaper', 'arabic_reshaper'),
        ('python-bidi', 'bidi'),
        ('Pillow', 'PIL')
    ]
    
    missing_modules = []
    
    for module_name, import_name in required_modules:
        try:
            __import__(import_name)
            print(f"   ✅ {module_name}")
        except ImportError:
            print(f"   ❌ {module_name} - مفقود")
            missing_modules.append(module_name)
    
    if missing_modules:
        print(f"\n⚠️ مكتبات مفقودة: {', '.join(missing_modules)}")
        print("لتثبيت المكتبات المفقودة:")
        for module in missing_modules:
            print(f"   pip install {module}")
        return False
    
    print("✅ جميع المتطلبات متوفرة")
    return True

def check_new_features():
    """فحص الميزات الجديدة"""
    print("🔍 فحص الميزات الجديدة...")
    
    features = [
        ("إدارة القوالب", "ui/template_dialog.py"),
        ("نظام الإعدادات", "ui/settings_dialog.py"),
        ("دعم العربية المحسن", "utils/arabic_support.py"),
        ("قاعدة البيانات المحدثة", "database/database_manager.py")
    ]
    
    all_ready = True
    
    for feature_name, file_path in features:
        file_full_path = project_root / file_path
        if file_full_path.exists():
            print(f"   ✅ {feature_name}")
        else:
            print(f"   ❌ {feature_name} - غير متوفر")
            all_ready = False
    
    return all_ready

def load_user_settings():
    """تحميل إعدادات المستخدم"""
    try:
        settings_file = project_root / "config" / "user_settings.json"
        
        if settings_file.exists():
            import json
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
            print(f"✅ تم تحميل إعدادات المستخدم")
            return settings
        else:
            print("ℹ️ لا توجد إعدادات مخصصة - سيتم استخدام الإعدادات الافتراضية")
            return {}
            
    except Exception as e:
        print(f"⚠️ خطأ في تحميل الإعدادات: {e}")
        return {}

def setup_application():
    """إعداد التطبيق"""
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        from PySide6.QtGui import QFont
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد خصائص التطبيق
        app.setApplicationName("نظام إدارة خطابات المستأجرين")
        app.setApplicationVersion("2.0.0")
        app.setOrganizationName("Desktop as3am 7tab")
        
        # إعداد الخط الافتراضي
        font = QFont("Tahoma", 12)
        app.setFont(font)
        
        # إعداد اتجاه التطبيق للعربية
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("✅ تم إعداد التطبيق بنجاح")
        return app
        
    except Exception as e:
        print(f"❌ خطأ في إعداد التطبيق: {e}")
        return None

def create_main_window(app, settings):
    """إنشاء النافذة الرئيسية"""
    try:
        from ui.main_window import MainWindow
        
        # إنشاء النافذة الرئيسية
        window = MainWindow()
        
        # تطبيق الإعدادات المحفوظة
        if settings:
            # إعدادات النافذة
            if settings.get("maximize_window", False):
                window.showMaximized()
            else:
                width = settings.get("window_width", 1200)
                height = settings.get("window_height", 800)
                window.resize(width, height)
            
            # إعدادات الخط
            font_family = settings.get("font_family", "Tahoma")
            font_size = settings.get("font_size", 12)
            font_bold = settings.get("font_bold", False)
            
            from PySide6.QtGui import QFont
            font = QFont(font_family, font_size)
            if font_bold:
                font.setBold(True)
            window.setFont(font)
        
        # عرض النافذة
        window.show()
        window.raise_()
        window.activateWindow()
        
        print("✅ تم إنشاء النافذة الرئيسية")
        return window
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النافذة الرئيسية: {e}")
        return None

def main():
    """الدالة الرئيسية"""
    print("🎯 نظام إدارة خطابات المستأجرين - الإصدار 2.0")
    print("=" * 60)
    
    # إعداد السجلات
    if not setup_enhanced_logging():
        print("⚠️ تحذير: لم يتم إعداد السجلات بشكل صحيح")
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ فشل في فحص المتطلبات")
        input("اضغط Enter للخروج...")
        return 1
    
    # فحص الميزات الجديدة
    if not check_new_features():
        print("\n⚠️ بعض الميزات الجديدة غير متوفرة")
        print("تأكد من تشغيل update_app.py أولاً")
    
    # تحميل إعدادات المستخدم
    settings = load_user_settings()
    
    print("\n🚀 بدء تشغيل التطبيق...")
    
    # إعداد التطبيق
    app = setup_application()
    if not app:
        print("❌ فشل في إعداد التطبيق")
        return 1
    
    # إنشاء النافذة الرئيسية
    window = create_main_window(app, settings)
    if not window:
        print("❌ فشل في إنشاء النافذة الرئيسية")
        return 1
    
    print("\n🎉 تم تشغيل التطبيق بنجاح!")
    print("الميزات المتاحة:")
    print("   📋 إدارة القوالب (Ctrl+T)")
    print("   ⚙️ الإعدادات (Ctrl+,)")
    print("   👥 إدارة المستأجرين")
    print("   📄 إنشاء الخطابات")
    print("   📱 إرسال واتساب")
    
    # تشغيل التطبيق
    try:
        return app.exec()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف التطبيق بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
