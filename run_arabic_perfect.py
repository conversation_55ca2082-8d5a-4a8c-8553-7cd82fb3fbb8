# -*- coding: utf-8 -*-
"""
تشغيل التطبيق مع إصلاح مثالي للنصوص العربية
Perfect Arabic Text Fix Application Launcher
"""

import sys
import os
from pathlib import Path

# إعداد المسارات
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_perfect_arabic_environment():
    """إعداد بيئة مثالية للنصوص العربية"""
    print("🔧 إعداد بيئة مثالية للنصوص العربية...")
    
    try:
        # إعداد متغيرات البيئة للـ RTL والعربية
        os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
        os.environ['QT_ENABLE_HIGHDPI_SCALING'] = '1'
        os.environ['QT_SCALE_FACTOR'] = '1'
        os.environ['QT_FONT_DPI'] = '96'
        os.environ['LC_ALL'] = 'ar_SA.UTF-8'  # اللغة العربية
        
        print("✅ تم إعداد متغيرات البيئة")
        return True
        
    except Exception as e:
        print(f"⚠️ تحذير في إعداد البيئة: {e}")
        return True  # متابعة حتى لو فشل

def test_arabic_libraries():
    """اختبار مكتبات اللغة العربية"""
    print("🔍 اختبار مكتبات اللغة العربية...")
    
    try:
        # اختبار arabic-reshaper
        import arabic_reshaper
        test_text = "أحمد محمد"
        reshaped = arabic_reshaper.reshape(test_text)
        print(f"   ✅ arabic-reshaper: '{test_text}' -> '{reshaped}'")
        
        # اختبار python-bidi
        from bidi.algorithm import get_display
        display_text = get_display(reshaped, base_dir='R')
        print(f"   ✅ python-bidi: '{reshaped}' -> '{display_text}'")
        
        # اختبار دعم اللغة العربية المحسن
        from utils.arabic_support import ArabicSupport
        final_text = ArabicSupport.reshape_arabic_text(test_text)
        print(f"   ✅ ArabicSupport: '{test_text}' -> '{final_text}'")
        
        return True
        
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("لتثبيت المكتبات:")
        print("   pip install arabic-reshaper python-bidi")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المكتبات: {e}")
        return False

def run_perfect_arabic_app():
    """تشغيل التطبيق مع إصلاح مثالي للعربية"""
    print("🚀 تشغيل التطبيق مع إصلاح مثالي للعربية...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt, QLocale
        from PySide6.QtGui import QFont, QFontDatabase
        from utils.arabic_support import ArabicSupport
        from ui.main_window import MainWindow
        
        print("✅ تم استيراد المكتبات")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد اللغة العربية
        locale = QLocale(QLocale.Arabic, QLocale.SaudiArabia)
        QLocale.setDefault(locale)
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("✅ تم إعداد اللغة العربية")
        
        # إعداد الخطوط العربية
        font_db = QFontDatabase()
        available_fonts = font_db.families()
        
        # البحث عن أفضل خط عربي متاح
        arabic_fonts = ["Tahoma", "Arial", "Amiri", "Cairo", "Noto Naskh Arabic", "Traditional Arabic"]
        selected_font = "Arial"  # افتراضي
        
        for font_name in arabic_fonts:
            if font_name in available_fonts:
                selected_font = font_name
                break
        
        # إعداد الخط الافتراضي
        app_font = QFont(selected_font, 12)
        app_font.setStyleHint(QFont.SansSerif)
        app_font.setStyleStrategy(QFont.PreferAntialias)
        app_font.setKerning(True)
        app.setFont(app_font)
        
        print(f"✅ تم إعداد الخط: {selected_font}")
        
        # إعداد دعم اللغة العربية
        ArabicSupport.setup_application_rtl(app)
        print("✅ تم إعداد دعم اللغة العربية")
        
        # تطبيق stylesheet شامل للعربية
        global_arabic_style = f"""
            QApplication {{
                font-family: '{selected_font}', 'Arial', 'Tahoma';
                font-size: 12px;
            }}
            
            QMainWindow {{
                background-color: #f8f9fa;
            }}
            
            QTableWidget {{
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                font-family: '{selected_font}', 'Arial', 'Tahoma';
                font-size: 12px;
                selection-background-color: #007bff;
                border: 1px solid #dee2e6;
                border-radius: 4px;
            }}
            
            QTableWidget::item {{
                padding: 10px 8px;
                border-bottom: 1px solid #e9ecef;
                text-align: right;
            }}
            
            QTableWidget::item:selected {{
                background-color: #007bff;
                color: white;
            }}
            
            QTableWidget::item:hover {{
                background-color: #e3f2fd;
            }}
            
            QHeaderView::section {{
                text-align: right;
                padding: 12px 8px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-family: '{selected_font}', 'Arial', 'Tahoma';
                font-size: 12px;
            }}
            
            QHeaderView::section:hover {{
                background-color: #e9ecef;
            }}
            
            QLineEdit {{
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-family: '{selected_font}', 'Arial', 'Tahoma';
                font-size: 12px;
                text-align: right;
            }}
            
            QLineEdit:focus {{
                border-color: #007bff;
                box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            }}
            
            QPushButton {{
                padding: 8px 16px;
                border: 1px solid #007bff;
                border-radius: 4px;
                background-color: #007bff;
                color: white;
                font-family: '{selected_font}', 'Arial', 'Tahoma';
                font-size: 12px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: #0056b3;
                border-color: #0056b3;
            }}
            
            QPushButton:pressed {{
                background-color: #004085;
                border-color: #004085;
            }}
            
            QLabel {{
                font-family: '{selected_font}', 'Arial', 'Tahoma';
                font-size: 12px;
                color: #495057;
            }}
        """
        
        app.setStyleSheet(global_arabic_style)
        print("✅ تم تطبيق stylesheet شامل")
        
        # إنشاء النافذة الرئيسية
        window = MainWindow()
        window.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق إصلاحات إضافية على النافذة
        if hasattr(window, 'tenants_table'):
            table = window.tenants_table
            
            # فرض RTL على الجدول
            table.setLayoutDirection(Qt.RightToLeft)
            
            # إعداد الرؤوس
            h_header = table.horizontalHeader()
            if h_header:
                h_header.setLayoutDirection(Qt.RightToLeft)
                h_header.setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter)
            
            v_header = table.verticalHeader()
            if v_header:
                v_header.setLayoutDirection(Qt.RightToLeft)
            
            print("✅ تم إصلاح الجدول")
        
        # عرض النافذة
        window.show()
        window.raise_()
        window.activateWindow()
        
        print("\n🎉 تم تشغيل التطبيق مع إصلاح مثالي للعربية!")
        print("=" * 60)
        print("الإصلاحات المطبقة:")
        print("✅ بيئة عربية مثالية")
        print("✅ مكتبات تشكيل النصوص")
        print(f"✅ خط عربي محسن: {selected_font}")
        print("✅ RTL كامل للتطبيق")
        print("✅ stylesheet احترافي")
        print("✅ إصلاح خاص للجداول")
        
        print("\n📋 تحقق من الجدول:")
        print("   النصوص العربية من اليمين لليسار")
        print("   الأحرف مترابطة وليست مقطعة")
        print("   الخط واضح ومقروء")
        print("   المحاذاة صحيحة")
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """الدالة الرئيسية"""
    print("🔧 تشغيل التطبيق مع إصلاح مثالي للنصوص العربية")
    print("=" * 70)
    
    # إعداد البيئة
    setup_perfect_arabic_environment()
    
    # اختبار المكتبات
    if not test_arabic_libraries():
        print("\n❌ فشل في اختبار المكتبات")
        print("يرجى تثبيت المكتبات المطلوبة:")
        print("   pip install arabic-reshaper python-bidi")
        return 1
    
    # تشغيل التطبيق
    return run_perfect_arabic_app()

if __name__ == "__main__":
    exit_code = main()
    
    if exit_code == 0:
        print("\n✅ تم إغلاق التطبيق بنجاح")
        print("🔤 النصوص العربية تعمل بشكل مثالي!")
    else:
        print(f"\n❌ حدث خطأ: {exit_code}")
        print("💡 جرب البدائل:")
        print("   python force_rtl_fix.py")
        print("   python run_arabic_fixed.py")
    
    input("اضغط Enter للخروج...")
    sys.exit(exit_code)
