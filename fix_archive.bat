@echo off
chcp 65001 >nul
title إصلاح مشكلة الأرشيف الفارغ

echo.
echo ================================================================
echo    إصلاح مشكلة الأرشيف الفارغ
echo    إضافة بيانات تجريبية وإصلاح الوظائف
echo ================================================================
echo.

echo 🔧 بدء إصلاح الأرشيف...
echo.

python fix_archive.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في إصلاح الأرشيف
    echo.
    echo 💡 جرب الحلول التالية:
    echo.
    echo 1. إنشاء بيانات تجريبية:
    echo    python create_sample_archive.py
    echo.
    echo 2. تشغيل التطبيق مباشرة:
    echo    python run_arabic_perfect.py
    echo.
    echo 3. إعادة تشغيل الإصلاح:
    echo    python fix_archive.py
    echo.
    pause
) else (
    echo.
    echo ✅ تم إصلاح الأرشيف بنجاح!
    echo 📋 الأرشيف يحتوي الآن على بيانات تجريبية
    echo 🔤 النصوص العربية تظهر بشكل صحيح
    echo.
    echo 🚀 لتشغيل التطبيق:
    echo    python run_arabic_perfect.py
)

echo.
pause
