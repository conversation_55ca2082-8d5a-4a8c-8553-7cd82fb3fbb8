# -*- coding: utf-8 -*-
"""
أدوات التعامل مع الملفات
File Utilities
"""

import os
import shutil
from pathlib import Path
from typing import Optional, List
import logging


class FileUtils:
    """فئة أدوات التعامل مع الملفات"""
    
    @staticmethod
    def ensure_directory_exists(directory_path: str) -> bool:
        """التأكد من وجود المجلد وإنشاؤه إذا لم يكن موجوداً"""
        try:
            Path(directory_path).mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            logging.error(f"خطأ في إنشاء المجلد {directory_path}: {e}")
            return False
    
    @staticmethod
    def copy_file(source_path: str, destination_path: str) -> bool:
        """نسخ ملف من مكان إلى آخر"""
        try:
            # التأكد من وجود مجلد الوجهة
            destination_dir = Path(destination_path).parent
            FileUtils.ensure_directory_exists(str(destination_dir))
            
            shutil.copy2(source_path, destination_path)
            return True
        except Exception as e:
            logging.error(f"خطأ في نسخ الملف من {source_path} إلى {destination_path}: {e}")
            return False
    
    @staticmethod
    def move_file(source_path: str, destination_path: str) -> bool:
        """نقل ملف من مكان إلى آخر"""
        try:
            # التأكد من وجود مجلد الوجهة
            destination_dir = Path(destination_path).parent
            FileUtils.ensure_directory_exists(str(destination_dir))
            
            shutil.move(source_path, destination_path)
            return True
        except Exception as e:
            logging.error(f"خطأ في نقل الملف من {source_path} إلى {destination_path}: {e}")
            return False
    
    @staticmethod
    def delete_file(file_path: str) -> bool:
        """حذف ملف"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
        except Exception as e:
            logging.error(f"خطأ في حذف الملف {file_path}: {e}")
            return False
    
    @staticmethod
    def get_file_size(file_path: str) -> Optional[int]:
        """الحصول على حجم الملف بالبايت"""
        try:
            if os.path.exists(file_path):
                return os.path.getsize(file_path)
            return None
        except Exception as e:
            logging.error(f"خطأ في الحصول على حجم الملف {file_path}: {e}")
            return None
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """تنسيق حجم الملف بوحدة مناسبة"""
        try:
            if size_bytes == 0:
                return "0 بايت"
            
            size_names = ["بايت", "كيلوبايت", "ميجابايت", "جيجابايت"]
            i = 0
            size = float(size_bytes)
            
            while size >= 1024.0 and i < len(size_names) - 1:
                size /= 1024.0
                i += 1
            
            return f"{size:.1f} {size_names[i]}"
            
        except Exception as e:
            logging.error(f"خطأ في تنسيق حجم الملف: {e}")
            return f"{size_bytes} بايت"
    
    @staticmethod
    def get_files_in_directory(directory_path: str, extension: str = None) -> List[str]:
        """الحصول على قائمة الملفات في مجلد"""
        try:
            if not os.path.exists(directory_path):
                return []
            
            files = []
            for file_name in os.listdir(directory_path):
                file_path = os.path.join(directory_path, file_name)
                if os.path.isfile(file_path):
                    if extension is None or file_name.lower().endswith(extension.lower()):
                        files.append(file_path)
            
            return sorted(files)
            
        except Exception as e:
            logging.error(f"خطأ في جلب ملفات المجلد {directory_path}: {e}")
            return []
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """تنظيف اسم الملف من الأحرف غير المسموحة"""
        try:
            # الأحرف غير المسموحة في أسماء الملفات
            invalid_chars = '<>:"/\\|?*'
            
            # استبدال الأحرف غير المسموحة
            sanitized = filename
            for char in invalid_chars:
                sanitized = sanitized.replace(char, '_')
            
            # إزالة المسافات الزائدة
            sanitized = sanitized.strip()
            
            # التأكد من عدم تجاوز الحد الأقصى لطول اسم الملف
            if len(sanitized) > 200:
                sanitized = sanitized[:200]
            
            return sanitized
            
        except Exception as e:
            logging.error(f"خطأ في تنظيف اسم الملف: {e}")
            return "ملف_جديد"
    
    @staticmethod
    def get_unique_filename(directory: str, base_name: str, extension: str) -> str:
        """الحصول على اسم ملف فريد في المجلد"""
        try:
            counter = 1
            original_name = f"{base_name}{extension}"
            file_path = os.path.join(directory, original_name)
            
            # إذا كان الملف غير موجود، إرجاع الاسم الأصلي
            if not os.path.exists(file_path):
                return original_name
            
            # البحث عن اسم فريد
            while os.path.exists(file_path):
                new_name = f"{base_name}_{counter}{extension}"
                file_path = os.path.join(directory, new_name)
                counter += 1
                
                # تجنب الحلقة اللانهائية
                if counter > 1000:
                    break
            
            return os.path.basename(file_path)
            
        except Exception as e:
            logging.error(f"خطأ في إنشاء اسم ملف فريد: {e}")
            return f"{base_name}_new{extension}"
