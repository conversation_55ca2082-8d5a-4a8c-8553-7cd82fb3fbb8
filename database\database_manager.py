# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات
Database Manager
"""

import sqlite3
import logging
from datetime import datetime
from typing import List, Optional
from pathlib import Path

from config.settings import DATABASE_PATH, create_directories
from database.models import Tenant, LetterTemplate, Letter, LetterArchive


class DatabaseManager:
    """مدير قاعدة البيانات - Database Manager"""

    def __init__(self):
        """تهيئة مدير قاعدة البيانات"""
        create_directories()
        self.db_path = DATABASE_PATH
        self.init_database()

    def get_connection(self) -> sqlite3.Connection:
        """الحصول على اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn

    def init_database(self):
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        try:
            with self.get_connection() as conn:
                # جدول المستأجرين - Tenants Table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS tenants (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        unit_number TEXT NOT NULL,
                        building_number TEXT NOT NULL,
                        phone_number TEXT,
                        email TEXT,
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # جدول قوالب الخطابات - Letter Templates Table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS letter_templates (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        template_type TEXT NOT NULL,
                        file_path TEXT NOT NULL,
                        description TEXT,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # جدول الخطابات - Letters Table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS letters (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        tenant_id INTEGER NOT NULL,
                        template_id INTEGER NOT NULL,
                        letter_number TEXT UNIQUE NOT NULL,
                        content TEXT NOT NULL,
                        output_path_docx TEXT,
                        output_path_pdf TEXT,
                        is_sent_whatsapp BOOLEAN DEFAULT 0,
                        sent_at TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (tenant_id) REFERENCES tenants (id),
                        FOREIGN KEY (template_id) REFERENCES letter_templates (id)
                    )
                """)

                # جدول أرشيف الخطابات - Letter Archive Table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS letter_archive (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        letter_id INTEGER NOT NULL,
                        tenant_name TEXT NOT NULL,
                        letter_number TEXT NOT NULL,
                        template_name TEXT NOT NULL,
                        created_date TEXT NOT NULL,
                        file_path TEXT NOT NULL,
                        status TEXT DEFAULT 'مرسل',
                        FOREIGN KEY (letter_id) REFERENCES letters (id)
                    )
                """)

                conn.commit()
                logging.info("تم تهيئة قاعدة البيانات بنجاح")

        except Exception as e:
            logging.error(f"خطأ في تهيئة قاعدة البيانات: {e}")
            raise

    # ===== إدارة المستأجرين - Tenants Management =====

    def add_tenant(self, tenant: Tenant) -> int:
        """إضافة مستأجر جديد"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    INSERT INTO tenants (name, unit_number, building_number,
                                       phone_number, email, notes)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (tenant.name, tenant.unit_number, tenant.building_number,
                      tenant.phone_number, tenant.email, tenant.notes))
                return cursor.lastrowid
        except Exception as e:
            logging.error(f"خطأ في إضافة المستأجر: {e}")
            raise

    def get_all_tenants(self) -> List[Tenant]:
        """الحصول على جميع المستأجرين"""
        try:
            with self.get_connection() as conn:
                rows = conn.execute("SELECT * FROM tenants ORDER BY name").fetchall()
                return [self._row_to_tenant(row) for row in rows]
        except Exception as e:
            logging.error(f"خطأ في جلب المستأجرين: {e}")
            return []

    def get_tenant_by_id(self, tenant_id: int) -> Optional[Tenant]:
        """الحصول على مستأجر بالمعرف"""
        try:
            with self.get_connection() as conn:
                row = conn.execute("SELECT * FROM tenants WHERE id = ?",
                                 (tenant_id,)).fetchone()
                return self._row_to_tenant(row) if row else None
        except Exception as e:
            logging.error(f"خطأ في جلب المستأجر: {e}")
            return None

    def update_tenant(self, tenant: Tenant) -> bool:
        """تحديث بيانات مستأجر"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    UPDATE tenants
                    SET name = ?, unit_number = ?, building_number = ?,
                        phone_number = ?, email = ?, notes = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (tenant.name, tenant.unit_number, tenant.building_number,
                      tenant.phone_number, tenant.email, tenant.notes, tenant.id))
                return True
        except Exception as e:
            logging.error(f"خطأ في تحديث المستأجر: {e}")
            return False

    def delete_tenant(self, tenant_id: int) -> bool:
        """حذف مستأجر"""
        try:
            with self.get_connection() as conn:
                conn.execute("DELETE FROM tenants WHERE id = ?", (tenant_id,))
                return True
        except Exception as e:
            logging.error(f"خطأ في حذف المستأجر: {e}")
            return False

    def search_tenants(self, search_term: str) -> List[Tenant]:
        """البحث في المستأجرين"""
        try:
            with self.get_connection() as conn:
                rows = conn.execute("""
                    SELECT * FROM tenants
                    WHERE name LIKE ? OR unit_number LIKE ? OR building_number LIKE ?
                    ORDER BY name
                """, (f"%{search_term}%", f"%{search_term}%", f"%{search_term}%")).fetchall()
                return [self._row_to_tenant(row) for row in rows]
        except Exception as e:
            logging.error(f"خطأ في البحث: {e}")
            return []

    def _row_to_tenant(self, row) -> Tenant:
        """تحويل صف قاعدة البيانات إلى كائن مستأجر"""
        return Tenant(
            id=row['id'],
            name=row['name'],
            unit_number=row['unit_number'],
            building_number=row['building_number'],
            phone_number=row['phone_number'] or "",
            email=row['email'] or "",
            notes=row['notes'] or "",
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
            updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
        )

    # ===== إدارة القوالب - Templates Management =====

    def add_template(self, template: LetterTemplate) -> int:
        """إضافة قالب جديد"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    INSERT INTO letter_templates (name, template_type, file_path,
                                                description, is_active)
                    VALUES (?, ?, ?, ?, ?)
                """, (template.name, template.template_type, template.file_path,
                      template.description, template.is_active))
                return cursor.lastrowid
        except Exception as e:
            logging.error(f"خطأ في إضافة القالب: {e}")
            raise

    def get_all_templates(self) -> List[LetterTemplate]:
        """الحصول على جميع القوالب"""
        try:
            with self.get_connection() as conn:
                rows = conn.execute("""
                    SELECT * FROM letter_templates
                    WHERE is_active = 1
                    ORDER BY name
                """).fetchall()
                return [self._row_to_template(row) for row in rows]
        except Exception as e:
            logging.error(f"خطأ في جلب القوالب: {e}")
            return []

    def _row_to_template(self, row) -> LetterTemplate:
        """تحويل صف قاعدة البيانات إلى كائن قالب"""
        return LetterTemplate(
            id=row['id'],
            name=row['name'],
            template_type=row['template_type'],
            file_path=row['file_path'],
            description=row['description'] or "",
            is_active=bool(row['is_active']),
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
            updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
        )

    # ===== إدارة الخطابات - Letters Management =====

    def add_letter(self, letter: Letter) -> int:
        """إضافة خطاب جديد"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    INSERT INTO letters (tenant_id, template_id, letter_number,
                                       content, output_path_docx, output_path_pdf)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (letter.tenant_id, letter.template_id, letter.letter_number,
                      letter.content, letter.output_path_docx, letter.output_path_pdf))
                return cursor.lastrowid
        except Exception as e:
            logging.error(f"خطأ في إضافة الخطاب: {e}")
            raise

    def generate_letter_number(self) -> str:
        """توليد رقم خطاب فريد"""
        try:
            with self.get_connection() as conn:
                # الحصول على آخر رقم خطاب
                row = conn.execute("""
                    SELECT letter_number FROM letters
                    ORDER BY id DESC LIMIT 1
                """).fetchone()

                if row:
                    # استخراج الرقم من آخر خطاب
                    last_number = row['letter_number']
                    if last_number.startswith("خطاب-"):
                        try:
                            number = int(last_number.split("-")[1]) + 1
                        except:
                            number = 1
                    else:
                        number = 1
                else:
                    number = 1

                return f"خطاب-{number:04d}"

        except Exception as e:
            logging.error(f"خطأ في توليد رقم الخطاب: {e}")
            return f"خطاب-{datetime.now().strftime('%Y%m%d%H%M%S')}"

    def get_all_letters(self) -> List[Letter]:
        """الحصول على جميع الخطابات"""
        try:
            with self.get_connection() as conn:
                rows = conn.execute("""
                    SELECT * FROM letters
                    ORDER BY created_at DESC
                """).fetchall()
                return [self._row_to_letter(row) for row in rows]
        except Exception as e:
            logging.error(f"خطأ في جلب الخطابات: {e}")
            return []

    def _row_to_letter(self, row) -> Letter:
        """تحويل صف قاعدة البيانات إلى كائن خطاب"""
        return Letter(
            id=row['id'],
            tenant_id=row['tenant_id'],
            template_id=row['template_id'],
            letter_number=row['letter_number'],
            content=row['content'],
            output_path_docx=row['output_path_docx'] or "",
            output_path_pdf=row['output_path_pdf'] or "",
            is_sent_whatsapp=bool(row['is_sent_whatsapp']),
            sent_at=datetime.fromisoformat(row['sent_at']) if row['sent_at'] else None,
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
            updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
        )
