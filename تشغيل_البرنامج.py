# -*- coding: utf-8 -*-
"""
ملف تشغيل مبسط لنظام إدارة خطابات المستأجرين
Simple launcher for Tenant Letters Management System
"""

import sys
import os
from pathlib import Path

def main():
    """تشغيل البرنامج الرئيسي"""
    try:
        print("=" * 50)
        print("نظام إدارة خطابات المستأجرين")
        print("Tenant Letters Management System")
        print("=" * 50)
        print()
        
        # التحقق من وجود الملف الرئيسي
        main_file = Path("main.py")
        if not main_file.exists():
            print("❌ خطأ: لم يتم العثور على الملف الرئيسي main.py")
            input("اضغط Enter للخروج...")
            return 1
        
        print("🚀 جاري تشغيل البرنامج...")
        print("📝 ملاحظة: إذا لم تظهر النافذة، تحقق من شريط المهام")
        print()
        
        # تشغيل البرنامج الرئيسي
        os.system("python main.py")
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البرنامج بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        input("اضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
