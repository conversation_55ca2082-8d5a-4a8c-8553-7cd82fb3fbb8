# -*- coding: utf-8 -*-
"""
اختبار تشكيل النصوص العربية المحسن
Enhanced Arabic Text Shaping Test
"""

import sys
from pathlib import Path

# إعداد المسارات
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_arabic_shaping():
    """اختبار تشكيل النصوص العربية"""
    print("🔤 اختبار تشكيل النصوص العربية المحسن")
    print("=" * 50)
    
    try:
        from utils.arabic_support import ArabicSupport
        
        # نصوص اختبار مختلفة
        test_texts = [
            "أحمد محمد جعفر",
            "فاطمة عبدالرحمن",
            "محمد عبدالله الأحمد",
            "مستأجر جديد",
            "ملاحظات مهمة",
            "تم التجديد",
            "بحاجة لمتابعة",
            "عقد منتهي",
            "دفع متأخر",
            "اتصال ضروري"
        ]
        
        print("📝 اختبار تشكيل النصوص:")
        print("-" * 30)
        
        for i, text in enumerate(test_texts, 1):
            print(f"{i:2d}. النص الأصلي: '{text}'")
            
            # اختبار التشكيل
            shaped = ArabicSupport.reshape_arabic_text(text)
            print(f"    النص المُشكل: '{shaped}'")
            
            # فحص إذا كان يحتوي على أحرف عربية
            has_arabic = ArabicSupport._contains_arabic_chars(text)
            print(f"    يحتوي على عربية: {'✅' if has_arabic else '❌'}")
            
            print()
        
        print("✅ تم اختبار تشكيل النصوص بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التشكيل: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_table_display():
    """اختبار عرض النصوص في جدول"""
    print("\n📋 اختبار عرض النصوص في جدول")
    print("=" * 50)
    
    try:
        from PySide6.QtWidgets import QApplication, QMainWindow, QTableWidget, QTableWidgetItem, QVBoxLayout, QWidget
        from PySide6.QtCore import Qt
        from PySide6.QtGui import QFont
        from utils.arabic_support import ArabicSupport
        
        app = QApplication(sys.argv)
        
        # إعداد RTL للتطبيق
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء نافذة اختبار
        window = QMainWindow()
        window.setWindowTitle("اختبار تشكيل النصوص العربية في الجدول")
        window.resize(800, 500)
        window.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء جدول
        central_widget = QWidget()
        layout = QVBoxLayout(central_widget)
        
        table = QTableWidget()
        table.setColumnCount(3)
        table.setRowCount(5)
        
        # إعداد الجدول للـ RTL (حسب الاقتراحات)
        table.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد الرؤوس
        headers = ["الاسم", "رقم الهاتف", "الملاحظات"]
        shaped_headers = [ArabicSupport.reshape_arabic_text(header) for header in headers]
        table.setHorizontalHeaderLabels(shaped_headers)
        
        # إعداد الرأس للـ RTL
        header = table.horizontalHeader()
        if header:
            header.setLayoutDirection(Qt.RightToLeft)
            header.setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter)
        
        # بيانات اختبار
        test_data = [
            ["أحمد محمد جعفر", "0501234567", "مستأجر جديد"],
            ["فاطمة عبدالرحمن", "0509876543", "مستأجرة قديمة"],
            ["محمد عبدالله الأحمد", "0551122334", "تم التجديد"],
            ["عائشة حسن علي", "0555667788", "بحاجة لمتابعة"],
            ["يوسف أحمد محمد", "0544556677", "عقد منتهي"]
        ]
        
        # إضافة البيانات للجدول
        for row, row_data in enumerate(test_data):
            for col, text in enumerate(row_data):
                # تشكيل النص العربي
                shaped_text = ArabicSupport.reshape_arabic_text(text)
                item = QTableWidgetItem(shaped_text)
                
                # تطبيق إعدادات RTL والخط (حسب الاقتراحات)
                item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                
                # إعداد خط يدعم العربية
                font = QFont()
                font_families = ["Tahoma", "Arial", "Amiri", "Cairo", "Noto Naskh Arabic"]
                for font_family in font_families:
                    font.setFamily(font_family)
                    if font.exactMatch():
                        break
                
                font.setPointSize(12)
                font.setStyleHint(QFont.SansSerif)
                font.setStyleStrategy(QFont.PreferAntialias)
                font.setKerning(True)
                item.setFont(font)
                
                table.setItem(row, col, item)
        
        # تطبيق stylesheet محسن (حسب الاقتراحات)
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                font-family: 'Tahoma', 'Arial', 'Amiri', 'Cairo';
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 10px 8px;
                border-bottom: 1px solid #e9ecef;
                text-align: right;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                text-align: right;
                padding: 12px 8px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-family: 'Tahoma', 'Arial', 'Amiri';
                font-size: 12px;
            }
        """)
        
        layout.addWidget(table)
        window.setCentralWidget(central_widget)
        
        # عرض النافذة
        window.show()
        window.raise_()
        window.activateWindow()
        
        print("✅ تم إنشاء جدول الاختبار")
        print("📋 تحقق من:")
        print("   ✅ النصوص العربية من اليمين لليسار")
        print("   ✅ الأحرف مترابطة وليست مقطعة")
        print("   ✅ الخط واضح ومقروء")
        print("   ✅ المحاذاة صحيحة")
        
        print("\n🎯 النافذة ستظهر لمدة 5 ثوان...")
        
        # إغلاق تلقائي بعد 5 ثوان
        from PySide6.QtCore import QTimer
        QTimer.singleShot(5000, app.quit)
        
        # تشغيل التطبيق
        app.exec()
        
        print("✅ تم إغلاق نافذة الاختبار")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الجدول: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار تشكيل النصوص العربية المحسن")
    print("=" * 60)
    
    # اختبار التشكيل
    if not test_arabic_shaping():
        print("❌ فشل اختبار التشكيل")
        return 1
    
    # اختبار عرض الجدول
    if not test_table_display():
        print("❌ فشل اختبار الجدول")
        return 1
    
    print("\n" + "=" * 60)
    print("🎉 جميع الاختبارات نجحت!")
    print("\nالإصلاحات المطبقة:")
    print("✅ تشكيل النصوص العربية محسن")
    print("✅ دعم خطوط عربية متعددة")
    print("✅ RTL كامل للجداول")
    print("✅ محاذاة صحيحة")
    print("✅ stylesheet محسن")
    
    print("\n🚀 يمكنك الآن تشغيل التطبيق:")
    print("   python run_arabic_fixed.py")
    print("   python force_rtl_fix.py")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    
    if exit_code == 0:
        print("\n✅ جميع الاختبارات نجحت")
        print("🔤 النصوص العربية تعمل بشكل مثالي!")
    else:
        print(f"\n❌ فشل في الاختبار: {exit_code}")
    
    input("اضغط Enter للخروج...")
    sys.exit(exit_code)
