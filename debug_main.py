# -*- coding: utf-8 -*-
"""
نسخة تشخيصية لنظام إدارة خطابات المستأجرين
Debug Version for Tenant Letters Management System
"""

import sys
import traceback
from pathlib import Path

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """الدالة الرئيسية مع تشخيص مفصل"""
    try:
        print("=" * 60)
        print("🚀 بدء تشغيل نظام إدارة خطابات المستأجرين")
        print("=" * 60)
        
        # الخطوة 1: التحقق من Python
        print(f"🐍 إصدار Python: {sys.version}")
        print(f"📁 مسار المشروع: {project_root}")
        
        # الخطوة 2: التحقق من المكتبات
        print("\n📦 التحقق من المكتبات المطلوبة...")
        
        required_modules = {
            'PySide6': 'واجهة المستخدم الرسومية',
            'docx': 'معالجة ملفات Word',
            'arabic_reshaper': 'دعم اللغة العربية',
            'bidi': 'الاتجاه الثنائي'
        }
        
        for module, description in required_modules.items():
            try:
                __import__(module)
                print(f"   ✅ {module} - {description}")
            except ImportError as e:
                print(f"   ❌ {module} - غير مثبت: {e}")
                return 1
        
        # الخطوة 3: استيراد وحدات التطبيق
        print("\n🔧 تحميل وحدات التطبيق...")
        
        try:
            from PySide6.QtWidgets import QApplication, QMessageBox
            from PySide6.QtCore import Qt
            from PySide6.QtGui import QFont
            print("   ✅ تم تحميل PySide6")
        except Exception as e:
            print(f"   ❌ خطأ في تحميل PySide6: {e}")
            return 1
        
        try:
            from config.settings import APP_NAME, APP_VERSION, create_directories
            print(f"   ✅ تم تحميل الإعدادات - {APP_NAME}")
        except Exception as e:
            print(f"   ❌ خطأ في تحميل الإعدادات: {e}")
            traceback.print_exc()
            return 1
        
        try:
            from utils.template_creator import TemplateCreator
            print("   ✅ تم تحميل منشئ القوالب")
        except Exception as e:
            print(f"   ❌ خطأ في تحميل منشئ القوالب: {e}")
            traceback.print_exc()
            return 1
        
        try:
            from ui.main_window import MainWindow
            print("   ✅ تم تحميل النافذة الرئيسية")
        except Exception as e:
            print(f"   ❌ خطأ في تحميل النافذة الرئيسية: {e}")
            traceback.print_exc()
            return 1
        
        # الخطوة 4: إنشاء التطبيق
        print("\n🖥️ إنشاء التطبيق...")
        
        try:
            app = QApplication(sys.argv)
            print("   ✅ تم إنشاء QApplication")
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء QApplication: {e}")
            traceback.print_exc()
            return 1
        
        # إعداد التطبيق
        app.setApplicationName(APP_NAME)
        app.setApplicationVersion(APP_VERSION)
        app.setOrganizationName("Desktop as3am 7tab")
        
        # إعداد الخط والاتجاه
        font = QFont("Tahoma", 10)
        app.setFont(font)
        app.setLayoutDirection(Qt.RightToLeft)
        print("   ✅ تم إعداد خصائص التطبيق")
        
        # الخطوة 5: إعداد النظام
        print("\n⚙️ إعداد النظام...")
        
        try:
            create_directories()
            print("   ✅ تم إنشاء المجلدات المطلوبة")
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء المجلدات: {e}")
            traceback.print_exc()
        
        try:
            TemplateCreator.create_all_default_templates()
            print("   ✅ تم إنشاء القوالب الافتراضية")
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء القوالب: {e}")
            traceback.print_exc()
        
        # الخطوة 6: إنشاء النافذة الرئيسية
        print("\n🏠 إنشاء النافذة الرئيسية...")
        
        try:
            main_window = MainWindow()
            print("   ✅ تم إنشاء النافذة الرئيسية")
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء النافذة الرئيسية: {e}")
            traceback.print_exc()
            return 1
        
        try:
            main_window.show()
            print("   ✅ تم عرض النافذة الرئيسية")
        except Exception as e:
            print(f"   ❌ خطأ في عرض النافذة: {e}")
            traceback.print_exc()
            return 1
        
        # الخطوة 7: تشغيل التطبيق
        print("\n🎯 تشغيل التطبيق...")
        print("📝 إذا لم تظهر النافذة، تحقق من شريط المهام")
        print("🔄 التطبيق يعمل الآن... (اضغط Ctrl+C للإيقاف)")
        print("=" * 60)
        
        # تشغيل حلقة الأحداث
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        return 0
        
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        traceback.print_exc()
        
        # محاولة إظهار رسالة خطأ
        try:
            if 'app' in locals():
                QMessageBox.critical(
                    None, "خطأ في التطبيق",
                    f"حدث خطأ غير متوقع:\n\n{e}"
                )
        except:
            pass
        
        input("\nاضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
