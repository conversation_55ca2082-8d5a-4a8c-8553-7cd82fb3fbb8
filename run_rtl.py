# -*- coding: utf-8 -*-
"""
تشغيل التطبيق مع دعم RTL كامل
RTL Enhanced Application Launcher
"""

import sys
import os
from pathlib import Path

# إعداد المسارات
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """الدالة الرئيسية مع دعم RTL كامل"""
    print("🌟 نظام إدارة خطابات المستأجرين - دعم RTL كامل")
    print("=" * 60)
    
    try:
        # استيراد المكتبات المطلوبة
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        from utils.arabic_support import ArabicSupport
        from ui.main_window import MainWindow
        
        print("🔍 فحص المكتبات...")
        print("✅ PySide6 متوفر")
        print("✅ دعم اللغة العربية متوفر")
        
        # إنشاء التطبيق
        print("\n🚀 إنشاء التطبيق...")
        app = QApplication(sys.argv)
        
        # إعداد خصائص التطبيق
        app.setApplicationName("نظام إدارة خطابات المستأجرين")
        app.setApplicationVersion("2.0 RTL")
        app.setOrganizationName("Desktop as3am 7tab")
        
        print("✅ تم إنشاء التطبيق")
        
        # إعداد دعم RTL والعربية الكامل
        print("🔤 إعداد دعم RTL والعربية...")
        if ArabicSupport.setup_application_rtl(app):
            print("✅ تم إعداد RTL بنجاح")
            print("   📝 النصوص ستظهر من اليمين لليسار")
            print("   🎨 تم تطبيق التصميم الاحترافي")
            print("   🔤 تم إعداد الخطوط العربية")
        else:
            print("⚠️ تحذير: مشكلة في إعداد RTL")
        
        # إنشاء النافذة الرئيسية
        print("\n🏠 إنشاء النافذة الرئيسية...")
        window = MainWindow()
        
        # تطبيق إعدادات إضافية للنافذة
        window.setLayoutDirection(Qt.RightToLeft)
        
        # عرض النافذة
        window.show()
        window.raise_()
        window.activateWindow()
        
        print("✅ تم عرض النافذة الرئيسية")
        
        print("\n🎉 تم تشغيل التطبيق بنجاح!")
        print("المميزات المفعلة:")
        print("   ✅ دعم RTL كامل (من اليمين لليسار)")
        print("   ✅ خطوط عربية محسنة")
        print("   ✅ واجهة احترافية")
        print("   ✅ تصميم متجاوب")
        print("   ✅ قوائم وأزرار بالعربية")
        
        print("\nالاختصارات المتاحة:")
        print("   📋 Ctrl+T - إدارة القوالب")
        print("   ⚙️ Ctrl+, - الإعدادات")
        print("   👥 Ctrl+N - مستأجر جديد")
        print("   📄 Ctrl+L - خطاب جديد")
        
        # تشغيل التطبيق
        return app.exec()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("\nلحل هذه المشكلة:")
        print("pip install PySide6 python-docx arabic-reshaper python-bidi")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        print("\nجرب الحلول التالية:")
        print("1. python update_app.py")
        print("2. python start_app.py")
        return 1

if __name__ == "__main__":
    exit_code = main()
    
    if exit_code == 0:
        print("\n✅ تم إغلاق التطبيق بنجاح")
    else:
        print(f"\n❌ التطبيق توقف مع رمز الخطأ: {exit_code}")
        input("اضغط Enter للخروج...")
    
    sys.exit(exit_code)
