@echo off
chcp 65001 >nul
title إصلاح خطأ التواريخ في الأرشيف

echo.
echo ================================================================
echo    إصلاح خطأ التواريخ في الأرشيف
echo    حل مشكلة: 'datetime' object is not subscriptable
echo ================================================================
echo.

echo 🔧 بدء إصلاح خطأ التواريخ...
echo.

python fix_date_error.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في إصلاح التواريخ
    echo.
    echo 💡 جرب الحلول البديلة:
    echo.
    echo 1. إصلاح الأرشيف العام:
    echo    python fix_archive.py
    echo.
    echo 2. التشغيل النظيف:
    echo    python run_clean.py
    echo.
    echo 3. إنشاء بيانات جديدة:
    echo    python create_sample_archive.py
    echo.
    pause
) else (
    echo.
    echo ✅ تم إصلاح خطأ التواريخ بنجاح!
    echo 📋 الأرشيف يعمل الآن بدون أخطاء
    echo 📅 التواريخ تظهر بشكل صحيح
    echo.
    echo 🚀 لتشغيل التطبيق:
    echo    python run_clean.py
)

echo.
pause
