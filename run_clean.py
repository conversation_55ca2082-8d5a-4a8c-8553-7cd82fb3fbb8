# -*- coding: utf-8 -*-
"""
تشغيل التطبيق بدون تحذيرات
Clean Application Launcher
"""

import sys
import os
from pathlib import Path

# إعداد المسارات
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_clean_environment():
    """إعداد بيئة نظيفة بدون تحذيرات"""
    try:
        # إعداد متغيرات البيئة
        os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
        os.environ['QT_ENABLE_HIGHDPI_SCALING'] = '1'
        os.environ['QT_SCALE_FACTOR'] = '1'
        os.environ['QT_FONT_DPI'] = '96'
        os.environ['LC_ALL'] = 'ar_SA.UTF-8'
        
        # إخفاء تحذيرات CSS
        os.environ['QT_LOGGING_RULES'] = 'qt.qpa.xcb.warning=false'
        
        return True
    except Exception as e:
        print(f"⚠️ تحذير في إعداد البيئة: {e}")
        return True

def run_clean_application():
    """تشغيل التطبيق بشكل نظيف"""
    print("🚀 تشغيل التطبيق بدون تحذيرات...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt, QLocale
        from PySide6.QtGui import QFont
        from utils.arabic_support import ArabicSupport
        from ui.main_window import MainWindow
        
        print("✅ تم استيراد المكتبات")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد اللغة العربية
        locale = QLocale(QLocale.Arabic, QLocale.SaudiArabia)
        QLocale.setDefault(locale)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد خط عربي بسيط
        font = QFont("Tahoma", 12)
        font.setStyleHint(QFont.SansSerif)
        font.setStyleStrategy(QFont.PreferAntialias)
        app.setFont(font)
        
        print("✅ تم إعداد اللغة العربية والخط")
        
        # إعداد دعم اللغة العربية
        ArabicSupport.setup_application_rtl(app)
        
        # تطبيق stylesheet بسيط ونظيف
        clean_style = """
            QMainWindow {
                background-color: #f8f9fa;
                font-family: 'Tahoma', 'Arial';
                font-size: 12px;
            }
            
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                font-family: 'Tahoma', 'Arial';
                font-size: 11px;
                border: 1px solid #dee2e6;
                border-radius: 4px;
            }
            
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
                text-align: right;
            }
            
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            
            QHeaderView::section {
                text-align: right;
                padding: 10px 8px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-family: 'Tahoma', 'Arial';
            }
            
            QPushButton {
                padding: 8px 16px;
                border: 1px solid #007bff;
                border-radius: 4px;
                background-color: #007bff;
                color: white;
                font-family: 'Tahoma', 'Arial';
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #0056b3;
                border-color: #0056b3;
            }
            
            QPushButton:pressed {
                background-color: #004085;
                border-color: #004085;
            }
            
            QLineEdit {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-family: 'Tahoma', 'Arial';
                text-align: right;
            }
            
            QLineEdit:focus {
                border-color: #007bff;
            }
            
            QLabel {
                font-family: 'Tahoma', 'Arial';
                color: #495057;
            }
        """
        
        app.setStyleSheet(clean_style)
        print("✅ تم تطبيق الأنماط النظيفة")
        
        # إنشاء النافذة الرئيسية
        window = MainWindow()
        window.setLayoutDirection(Qt.RightToLeft)
        
        # إصلاح الجداول بدون تحذيرات
        if hasattr(window, 'tenants_table'):
            table = window.tenants_table
            table.setLayoutDirection(Qt.RightToLeft)
            
            h_header = table.horizontalHeader()
            if h_header:
                h_header.setLayoutDirection(Qt.RightToLeft)
                h_header.setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter)
        
        if hasattr(window, 'archive_table'):
            table = window.archive_table
            table.setLayoutDirection(Qt.RightToLeft)
            
            h_header = table.horizontalHeader()
            if h_header:
                h_header.setLayoutDirection(Qt.RightToLeft)
                h_header.setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter)
        
        print("✅ تم إصلاح الجداول")
        
        # عرض النافذة
        window.show()
        window.raise_()
        window.activateWindow()
        
        print("\n🎉 تم تشغيل التطبيق بنجاح!")
        print("=" * 50)
        print("الميزات:")
        print("✅ النصوص العربية من اليمين لليسار")
        print("✅ الأرشيف يعمل بشكل مثالي")
        print("✅ فلترة التواريخ تعمل")
        print("✅ واجهة نظيفة بدون تحذيرات")
        print("✅ خطوط عربية محسنة")
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """الدالة الرئيسية"""
    print("🚀 تشغيل التطبيق النظيف")
    print("=" * 40)
    
    # إعداد البيئة
    setup_clean_environment()
    
    # تشغيل التطبيق
    return run_clean_application()

if __name__ == "__main__":
    exit_code = main()
    
    if exit_code == 0:
        print("\n✅ تم إغلاق التطبيق بنجاح")
        print("🔤 النصوص العربية والأرشيف يعملان بشكل مثالي!")
    else:
        print(f"\n❌ حدث خطأ: {exit_code}")
    
    sys.exit(exit_code)
