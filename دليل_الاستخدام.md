# دليل الاستخدام السريع - نظام إدارة خطابات المستأجرين

## 🚀 كيفية تشغيل البرنامج

### الطريقة الأولى (مستحسنة):
```
انقر نقراً مزدوجاً على ملف: run.bat
```

### الطريقة الثانية:
```
انقر نقراً مزدوجاً على ملف: تشغيل_البرنامج.py
```

### الطريقة الثالثة (للمطورين):
```
python main.py
```

## 📋 الخطوات الأولى

### 1. إضافة مستأجر جديد
- انقر على زر "إضافة مستأجر" في الشريط العلوي
- املأ البيانات المطلوبة:
  - **الاسم** (مطلوب)
  - **رقم الوحدة** (مطلوب)
  - **رقم العمارة** (مطلوب)
  - رقم الهاتف (اختياري)
  - البريد الإلكتروني (اختياري)
  - ملاحظات (اختياري)
- انقر "حفظ"

### 2. إنشاء خطاب
- انقر على زر "إنشاء خطاب" في الشريط العلوي
- اختر المستأجر من القائمة المنسدلة
- اختر القالب (سيتم استخدام القالب الافتراضي إذا لم تختر)
- اكتب محتوى الخطاب في المربع النصي
- حدد الخيارات:
  - ✅ إنشاء ملف PDF (مستحسن)
  - ✅ فتح واتساب للإرسال (إذا كان لديك رقم هاتف للمستأجر)
- انقر "إنشاء الخطاب"

### 3. البحث في المستأجرين
- استخدم مربع البحث في تبويب "المستأجرين"
- يمكنك البحث بالاسم أو رقم الوحدة أو رقم العمارة

## 📁 مجلدات البرنامج

- **`data/`** - قاعدة البيانات
- **`templates/`** - قوالب الخطابات
- **`output/`** - الخطابات المُنشأة
- **`logs/`** - ملفات السجلات

## 🔧 استكشاف الأخطاء

### مشكلة: البرنامج لا يعمل
**الحل:**
1. تأكد من تثبيت Python 3.8+
2. شغّل: `pip install -r requirements.txt`
3. شغّل: `python test_app.py` للتحقق من سلامة النظام

### مشكلة: لا تظهر النصوص العربية بشكل صحيح
**الحل:**
- تأكد من وجود خط Tahoma في النظام
- أعد تشغيل البرنامج

### مشكلة: فشل في إنشاء ملف PDF
**الحل:**
- تأكد من تثبيت Microsoft Word أو LibreOffice
- أو استخدم الخطابات بصيغة Word فقط

## 📞 الدعم

إذا واجهت أي مشاكل:
1. راجع ملف `logs/app.log` للتفاصيل
2. شغّل `python test_app.py` للتشخيص
3. تأكد من تثبيت جميع المتطلبات في `requirements.txt`

## ✨ نصائح للاستخدام

- **النسخ الاحتياطية**: انسخ مجلد `data/` بانتظام
- **القوالب**: ضع قوالب Word الخاصة بك في مجلد `templates/`
- **التنظيم**: استخدم أرقام وحدات وعمارات واضحة للبحث السهل
- **واتساب**: تأكد من كتابة أرقام الهواتف بالصيغة الصحيحة (مثل: 0501234567)

---
**تم التطوير بواسطة**: Desktop as3am 7tab  
**الإصدار**: 1.0.0
