# -*- coding: utf-8 -*-
"""
خدمة إرسال واتساب
WhatsApp Service
"""

import webbrowser
import urllib.parse
import logging
from typing import Optional

from database.models import Tenant
from config.settings import WHATSAPP_WEB_URL


class WhatsAppService:
    """خدمة إرسال واتساب"""
    
    @staticmethod
    def send_letter(tenant: Tenant, letter_path: str, message: str = "") -> bool:
        """
        إرسال خطاب عبر واتساب
        
        Args:
            tenant: بيانات المستأجر
            letter_path: مسار ملف الخطاب
            message: رسالة إضافية (اختياري)
            
        Returns:
            bool: نجح الإرسال أم لا
        """
        try:
            if not tenant.phone_number:
                logging.error("لا يوجد رقم هاتف للمستأجر")
                return False
            
            # تنظيف رقم الهاتف
            phone = WhatsAppService._clean_phone_number(tenant.phone_number)
            if not phone:
                logging.error("رقم الهاتف غير صحيح")
                return False
            
            # إعداد الرسالة
            if not message:
                message = f"السلام عليكم {tenant.name}،\n\nنرفق لكم خطاب إداري.\n\nشكراً لكم."
            
            # فتح واتساب ويب
            WhatsAppService._open_whatsapp_web(phone, message)
            
            logging.info(f"تم فتح واتساب للمستأجر: {tenant.name}")
            return True
            
        except Exception as e:
            logging.error(f"خطأ في إرسال واتساب: {e}")
            return False
    
    @staticmethod
    def _clean_phone_number(phone: str) -> Optional[str]:
        """تنظيف رقم الهاتف"""
        if not phone:
            return None
        
        # إزالة المسافات والأحرف غير الرقمية
        cleaned = ''.join(filter(str.isdigit, phone))
        
        # التحقق من طول الرقم
        if len(cleaned) < 10:
            return None
        
        # إضافة رمز الدولة إذا لم يكن موجوداً
        if not cleaned.startswith('966'):  # السعودية
            if cleaned.startswith('05'):
                cleaned = '966' + cleaned[1:]  # إزالة الصفر وإضافة رمز الدولة
            elif cleaned.startswith('5'):
                cleaned = '966' + cleaned
        
        return cleaned
    
    @staticmethod
    def _open_whatsapp_web(phone: str, message: str):
        """فتح واتساب ويب مع الرسالة"""
        try:
            # ترميز الرسالة
            encoded_message = urllib.parse.quote(message)
            
            # إنشاء رابط واتساب
            whatsapp_url = f"{WHATSAPP_WEB_URL}/send?phone={phone}&text={encoded_message}"
            
            # فتح الرابط في المتصفح
            webbrowser.open(whatsapp_url)
            
        except Exception as e:
            logging.error(f"خطأ في فتح واتساب ويب: {e}")
    
    @staticmethod
    def validate_phone_number(phone: str) -> bool:
        """التحقق من صحة رقم الهاتف"""
        cleaned = WhatsAppService._clean_phone_number(phone)
        return cleaned is not None and len(cleaned) >= 12
    
    @staticmethod
    def format_phone_number(phone: str) -> str:
        """تنسيق رقم الهاتف للعرض"""
        cleaned = WhatsAppService._clean_phone_number(phone)
        if not cleaned:
            return phone
        
        # تنسيق الرقم السعودي
        if cleaned.startswith('966') and len(cleaned) == 12:
            return f"+966 {cleaned[3:5]} {cleaned[5:8]} {cleaned[8:]}"
        
        return phone
