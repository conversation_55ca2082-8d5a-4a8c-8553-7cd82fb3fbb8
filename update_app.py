# -*- coding: utf-8 -*-
"""
تحديث شامل للتطبيق
Comprehensive Application Update
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def update_main_window():
    """تحديث النافذة الرئيسية"""
    print("🔄 تحديث النافذة الرئيسية...")
    
    try:
        # تحديث الاستيرادات
        main_window_file = project_root / "ui" / "main_window.py"
        
        if main_window_file.exists():
            print("✅ تم العثور على النافذة الرئيسية")
            
            # قراءة المحتوى
            with open(main_window_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التحقق من وجود الاستيرادات الجديدة
            if "TemplateDialog" in content and "SettingsDialog" in content:
                print("✅ الاستيرادات محدثة بالفعل")
            else:
                print("⚠️ الاستيرادات تحتاج تحديث")
        else:
            print("❌ لم يتم العثور على النافذة الرئيسية")
            
    except Exception as e:
        print(f"❌ خطأ في تحديث النافذة الرئيسية: {e}")

def update_database():
    """تحديث قاعدة البيانات"""
    print("🔄 تحديث قاعدة البيانات...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # التحقق من وجود الجداول
        with db_manager.get_connection() as conn:
            # التحقق من جدول القوالب
            cursor = conn.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='letter_templates'
            """)
            
            if cursor.fetchone():
                print("✅ جدول القوالب موجود")
            else:
                print("❌ جدول القوالب غير موجود")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")

def create_sample_templates():
    """إنشاء قوالب تجريبية"""
    print("🔄 إنشاء قوالب تجريبية...")
    
    try:
        from utils.template_creator import TemplateCreator
        
        templates = TemplateCreator.create_all_default_templates()
        
        if templates:
            print(f"✅ تم إنشاء {len(templates)} قالب افتراضي")
            for template in templates:
                print(f"   📄 {Path(template).name}")
        else:
            print("⚠️ لم يتم إنشاء قوالب جديدة")
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء القوالب: {e}")

def test_new_features():
    """اختبار الميزات الجديدة"""
    print("🔄 اختبار الميزات الجديدة...")
    
    try:
        # اختبار نافذة القوالب
        print("   📋 اختبار نافذة إدارة القوالب...")
        from ui.template_dialog import TemplateDialog
        print("   ✅ نافذة القوالب جاهزة")
        
        # اختبار نافذة الإعدادات
        print("   ⚙️ اختبار نافذة الإعدادات...")
        from ui.settings_dialog import SettingsDialog
        print("   ✅ نافذة الإعدادات جاهزة")
        
        # اختبار دعم اللغة العربية المحسن
        print("   🔤 اختبار دعم اللغة العربية...")
        from utils.arabic_support import ArabicSupport
        
        test_text = "مرحباً بكم في نظام إدارة خطابات المستأجرين المحدث"
        reshaped = ArabicSupport.fix_arabic_text_rendering(test_text)
        
        if reshaped:
            print("   ✅ دعم اللغة العربية يعمل بنجاح")
        else:
            print("   ⚠️ مشكلة في دعم اللغة العربية")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الميزات: {e}")

def create_user_guide():
    """إنشاء دليل المستخدم المحدث"""
    print("🔄 إنشاء دليل المستخدم المحدث...")
    
    try:
        guide_content = """# دليل المستخدم - الإصدار 2.0

## الميزات الجديدة

### 1. إدارة القوالب المتقدمة
- إضافة وتعديل وحذف القوالب
- فحص صحة القوالب تلقائياً
- معاينة القوالب قبل الاستخدام
- تصنيف القوالب حسب النوع

### 2. نظام الإعدادات الشامل
- إعدادات المظهر والخطوط
- إعدادات الخطابات والقوالب
- إعدادات واتساب المخصصة
- إعدادات متقدمة للأداء

### 3. تحسينات اللغة العربية
- دعم محسن للنصوص العربية
- خطوط عربية متعددة
- اتجاه RTL محسن
- تشكيل وربط الأحرف

## كيفية الاستخدام

### إدارة القوالب:
1. من القائمة الرئيسية: أدوات > إدارة القوالب
2. أو اضغط Ctrl+T
3. يمكنك إضافة قوالب جديدة أو تعديل الموجودة

### الإعدادات:
1. من القائمة الرئيسية: أدوات > الإعدادات  
2. أو اضغط Ctrl+,
3. اختر التبويب المناسب وعدّل الإعدادات

### نصائح للاستخدام الأمثل:
- استخدم خط Tahoma للحصول على أفضل عرض للعربية
- احفظ نسخ احتياطية من القوالب المخصصة
- فعّل الحفظ التلقائي في الإعدادات
"""
        
        guide_file = project_root / "دليل_المستخدم_v2.md"
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print(f"✅ تم إنشاء دليل المستخدم: {guide_file}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء دليل المستخدم: {e}")

def main():
    """الدالة الرئيسية للتحديث"""
    print("🚀 بدء تحديث التطبيق إلى الإصدار 2.0")
    print("=" * 50)
    
    # تحديث المكونات
    update_main_window()
    print()
    
    update_database()
    print()
    
    create_sample_templates()
    print()
    
    test_new_features()
    print()
    
    create_user_guide()
    print()
    
    print("=" * 50)
    print("🎉 تم تحديث التطبيق بنجاح!")
    print()
    print("الميزات الجديدة:")
    print("✅ إدارة القوالب المتقدمة")
    print("✅ نظام الإعدادات الشامل") 
    print("✅ تحسينات اللغة العربية")
    print("✅ واجهة مستخدم محسنة")
    print()
    print("لتشغيل التطبيق المحدث:")
    print("python start_app.py")

if __name__ == "__main__":
    main()
