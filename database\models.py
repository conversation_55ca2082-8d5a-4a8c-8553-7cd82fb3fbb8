# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات
Database Models
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional


@dataclass
class Tenant:
    """نموذج المستأجر - Tenant Model"""
    id: Optional[int] = None
    name: str = ""
    unit_number: str = ""
    building_number: str = ""
    phone_number: str = ""
    email: str = ""
    notes: str = ""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    def __post_init__(self):
        """تهيئة التواريخ عند إنشاء الكائن"""
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()


@dataclass
class LetterTemplate:
    """نموذج قالب الخطاب - Letter Template Model"""
    id: Optional[int] = None
    name: str = ""
    template_type: str = ""
    file_path: str = ""
    description: str = ""
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    def __post_init__(self):
        """تهيئة التواريخ عند إنشاء الكائن"""
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()


@dataclass
class Letter:
    """نموذج الخطاب - Letter Model"""
    id: Optional[int] = None
    tenant_id: int = 0
    template_id: int = 0
    letter_number: str = ""
    content: str = ""
    output_path_docx: str = ""
    output_path_pdf: str = ""
    is_sent_whatsapp: bool = False
    sent_at: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    def __post_init__(self):
        """تهيئة التواريخ عند إنشاء الكائن"""
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()


@dataclass
class LetterArchive:
    """نموذج أرشيف الخطابات - Letter Archive Model"""
    id: Optional[int] = None
    letter_id: int = 0
    tenant_name: str = ""
    letter_number: str = ""
    template_name: str = ""
    created_date: str = ""
    file_path: str = ""
    status: str = "مرسل"  # مرسل، مسودة، ملغي

    def __post_init__(self):
        """تهيئة البيانات عند إنشاء الكائن"""
        if not self.created_date:
            self.created_date = datetime.now().strftime("%Y-%m-%d")
