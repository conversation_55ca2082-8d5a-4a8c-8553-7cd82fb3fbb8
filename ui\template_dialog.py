# -*- coding: utf-8 -*-
"""
نافذة إدارة القوالب
Template Management Dialog
"""

import os
from pathlib import Path
from typing import List, Optional

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit,
    QPushButton, QTextEdit, QLabel, QMessageBox, QGroupBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView,
    QFileDialog, QComboBox, QSplitter, QFrame, QProgressBar
)
from PySide6.QtCore import Qt, Signal, QThread
from PySide6.QtGui import QFont, QIcon, QPixmap

from database.database_manager import DatabaseManager
from database.models import LetterTemplate
from utils.arabic_support import ArabicSupport
from services.letter_service import LetterService
from config.settings import TEMPLATES_DIR, TEMPLATE_TYPES, TEMPLATE_VARIABLES


class TemplateValidationWorker(QThread):
    """عامل التحقق من صحة القالب في خيط منفصل"""
    validation_complete = Signal(dict)

    def __init__(self, template_path: str):
        super().__init__()
        self.template_path = template_path
        self.letter_service = LetterService()

    def run(self):
        """تشغيل التحقق من صحة القالب"""
        try:
            result = self.letter_service.validate_template(self.template_path)
            self.validation_complete.emit(result)
        except Exception as e:
            self.validation_complete.emit({
                'valid': False,
                'error': str(e),
                'variables_found': [],
                'missing_variables': []
            })


class TemplateDialog(QDialog):
    """نافذة إدارة القوالب"""

    template_updated = Signal()  # إشارة عند تحديث القوالب

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.letter_service = LetterService()
        self.current_templates: List[LetterTemplate] = []
        self.validation_worker = None

        self.init_ui()
        self.setup_connections()
        self.load_templates()

        # إعداد دعم اللغة العربية
        ArabicSupport.setup_arabic_font(self, 12)
        ArabicSupport.setup_rtl_layout(self)

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة قوالب الخطابات")
        self.setModal(True)
        self.resize(1000, 700)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)

        # شريط الأدوات العلوي
        self.create_toolbar(main_layout)

        # المحتوى الرئيسي - تقسيم أفقي
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # الجانب الأيسر - قائمة القوالب
        self.create_templates_list(splitter)

        # الجانب الأيمن - تفاصيل القالب
        self.create_template_details(splitter)

        # تعيين نسب التقسيم
        splitter.setSizes([400, 600])

        # شريط الحالة
        self.status_bar = QLabel("جاهز")
        self.status_bar.setStyleSheet("""
            QLabel {
                padding: 5px;
                background-color: #f0f0f0;
                border-top: 1px solid #ccc;
                font-size: 11px;
            }
        """)
        main_layout.addWidget(self.status_bar)

    def create_toolbar(self, parent_layout):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-bottom: 1px solid #dee2e6;
                padding: 10px;
            }
        """)
        toolbar_layout = QHBoxLayout(toolbar_frame)

        # أزرار الإجراءات
        self.add_template_btn = QPushButton("إضافة قالب جديد")
        self.add_template_btn.setIcon(self.create_icon("➕"))
        self.add_template_btn.setStyleSheet(self.get_button_style("#28a745"))

        self.import_template_btn = QPushButton("استيراد قالب")
        self.import_template_btn.setIcon(self.create_icon("📁"))
        self.import_template_btn.setStyleSheet(self.get_button_style("#007bff"))

        self.edit_template_btn = QPushButton("تعديل")
        self.edit_template_btn.setIcon(self.create_icon("✏️"))
        self.edit_template_btn.setStyleSheet(self.get_button_style("#ffc107"))
        self.edit_template_btn.setEnabled(False)

        self.delete_template_btn = QPushButton("حذف")
        self.delete_template_btn.setIcon(self.create_icon("🗑️"))
        self.delete_template_btn.setStyleSheet(self.get_button_style("#dc3545"))
        self.delete_template_btn.setEnabled(False)

        self.refresh_btn = QPushButton("تحديث")
        self.refresh_btn.setIcon(self.create_icon("🔄"))
        self.refresh_btn.setStyleSheet(self.get_button_style("#6c757d"))

        # إضافة الأزرار للتخطيط
        toolbar_layout.addWidget(self.add_template_btn)
        toolbar_layout.addWidget(self.import_template_btn)
        toolbar_layout.addWidget(QFrame())  # فاصل
        toolbar_layout.addWidget(self.edit_template_btn)
        toolbar_layout.addWidget(self.delete_template_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.refresh_btn)

        parent_layout.addWidget(toolbar_frame)

    def create_templates_list(self, parent_splitter):
        """إنشاء قائمة القوالب"""
        left_frame = QFrame()
        left_layout = QVBoxLayout(left_frame)

        # عنوان القسم
        list_title = QLabel("قائمة القوالب")
        list_title.setFont(QFont("Tahoma", 12, QFont.Bold))
        list_title.setStyleSheet("padding: 10px; background-color: #e9ecef; border-radius: 5px;")
        left_layout.addWidget(list_title)

        # جدول القوالب
        self.templates_table = QTableWidget()
        self.templates_table.setColumnCount(4)
        self.templates_table.setHorizontalHeaderLabels([
            "اسم القالب", "النوع", "الحالة", "تاريخ الإنشاء"
        ])

        # إعداد الجدول
        header = self.templates_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)

        self.templates_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.templates_table.setAlternatingRowColors(True)
        self.templates_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)

        # إعداد دعم اللغة العربية للجدول
        ArabicSupport.setup_arabic_font(self.templates_table, 11)
        ArabicSupport.setup_rtl_layout(self.templates_table)

        left_layout.addWidget(self.templates_table)
        parent_splitter.addWidget(left_frame)

    def create_template_details(self, parent_splitter):
        """إنشاء قسم تفاصيل القالب"""
        right_frame = QFrame()
        right_layout = QVBoxLayout(right_frame)

        # عنوان القسم
        details_title = QLabel("تفاصيل القالب")
        details_title.setFont(QFont("Tahoma", 12, QFont.Bold))
        details_title.setStyleSheet("padding: 10px; background-color: #e9ecef; border-radius: 5px;")
        right_layout.addWidget(details_title)

        # نموذج تفاصيل القالب
        details_group = QGroupBox("معلومات القالب")
        details_layout = QFormLayout(details_group)

        self.template_name_input = QLineEdit()
        self.template_name_input.setPlaceholderText("أدخل اسم القالب...")
        details_layout.addRow("اسم القالب:", self.template_name_input)

        self.template_type_combo = QComboBox()
        self.template_type_combo.addItems(TEMPLATE_TYPES)
        details_layout.addRow("نوع القالب:", self.template_type_combo)

        self.template_description_input = QTextEdit()
        self.template_description_input.setPlaceholderText("وصف القالب (اختياري)...")
        self.template_description_input.setMaximumHeight(80)
        details_layout.addRow("الوصف:", self.template_description_input)

        self.template_file_label = QLabel("لم يتم اختيار ملف")
        self.template_file_label.setStyleSheet("color: #6c757d; font-style: italic;")
        self.choose_file_btn = QPushButton("اختيار ملف القالب")
        self.choose_file_btn.setStyleSheet(self.get_button_style("#17a2b8"))

        file_layout = QHBoxLayout()
        file_layout.addWidget(self.template_file_label)
        file_layout.addWidget(self.choose_file_btn)
        details_layout.addRow("ملف القالب:", file_layout)

        right_layout.addWidget(details_group)

        # قسم التحقق من صحة القالب
        validation_group = QGroupBox("التحقق من صحة القالب")
        validation_layout = QVBoxLayout(validation_group)

        self.validate_btn = QPushButton("فحص القالب")
        self.validate_btn.setStyleSheet(self.get_button_style("#fd7e14"))
        self.validate_btn.setEnabled(False)

        self.validation_progress = QProgressBar()
        self.validation_progress.setVisible(False)

        self.validation_result = QTextEdit()
        self.validation_result.setMaximumHeight(150)
        self.validation_result.setReadOnly(True)
        self.validation_result.setPlaceholderText("نتائج فحص القالب ستظهر هنا...")

        validation_layout.addWidget(self.validate_btn)
        validation_layout.addWidget(self.validation_progress)
        validation_layout.addWidget(self.validation_result)

        right_layout.addWidget(validation_group)

        # قسم المتغيرات المدعومة
        variables_group = QGroupBox("المتغيرات المدعومة")
        variables_layout = QVBoxLayout(variables_group)

        variables_text = QTextEdit()
        variables_text.setReadOnly(True)
        variables_text.setMaximumHeight(120)

        # إعداد نص المتغيرات
        variables_content = "المتغيرات التي يمكن استخدامها في القوالب:\n\n"
        for var, desc in TEMPLATE_VARIABLES.items():
            variables_content += f"{var} - {desc}\n"

        variables_text.setPlainText(variables_content)
        variables_text.setStyleSheet("background-color: #f8f9fa; border: 1px solid #dee2e6;")

        variables_layout.addWidget(variables_text)
        right_layout.addWidget(variables_group)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        self.save_template_btn = QPushButton("حفظ القالب")
        self.save_template_btn.setStyleSheet(self.get_button_style("#28a745"))
        self.save_template_btn.setEnabled(False)

        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setStyleSheet(self.get_button_style("#6c757d"))

        self.close_btn = QPushButton("إغلاق")
        self.close_btn.setStyleSheet(self.get_button_style("#dc3545"))

        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_template_btn)
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.close_btn)

        right_layout.addLayout(buttons_layout)

        # إعداد دعم اللغة العربية
        for widget in [self.template_name_input, self.template_description_input,
                      self.validation_result, variables_text]:
            ArabicSupport.setup_arabic_font(widget, 11)
            if hasattr(widget, 'setLayoutDirection'):
                ArabicSupport.setup_rtl_layout(widget)

        parent_splitter.addWidget(right_frame)

    def get_button_style(self, color: str) -> str:
        """الحصول على نمط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 11px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.2)};
            }}
            QPushButton:disabled {{
                background-color: #6c757d;
                color: #adb5bd;
            }}
        """

    def darken_color(self, color: str, factor: float = 0.1) -> str:
        """تغميق اللون"""
        # تحويل بسيط للألوان الشائعة
        color_map = {
            "#28a745": "#218838",
            "#007bff": "#0056b3",
            "#ffc107": "#e0a800",
            "#dc3545": "#c82333",
            "#6c757d": "#545b62",
            "#17a2b8": "#138496",
            "#fd7e14": "#e8650e"
        }
        return color_map.get(color, color)

    def create_icon(self, emoji: str) -> QIcon:
        """إنشاء أيقونة من رمز تعبيري"""
        # هذه دالة بسيطة - يمكن تحسينها لاحقاً
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        return QIcon(pixmap)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # أزرار شريط الأدوات
        self.add_template_btn.clicked.connect(self.add_new_template)
        self.import_template_btn.clicked.connect(self.import_template)
        self.edit_template_btn.clicked.connect(self.edit_selected_template)
        self.delete_template_btn.clicked.connect(self.delete_selected_template)
        self.refresh_btn.clicked.connect(self.load_templates)

        # أزرار التفاصيل
        self.choose_file_btn.clicked.connect(self.choose_template_file)
        self.validate_btn.clicked.connect(self.validate_template)
        self.save_template_btn.clicked.connect(self.save_template)
        self.cancel_btn.clicked.connect(self.cancel_edit)
        self.close_btn.clicked.connect(self.close)

        # جدول القوالب
        self.templates_table.selectionModel().selectionChanged.connect(
            self.on_template_selection_changed
        )
        self.templates_table.doubleClicked.connect(self.edit_selected_template)

        # حقول الإدخال
        self.template_name_input.textChanged.connect(self.validate_form)
        self.template_type_combo.currentTextChanged.connect(self.validate_form)

    def load_templates(self):
        """تحميل قائمة القوالب"""
        try:
            self.current_templates = self.db_manager.get_all_templates()
            self.update_templates_table()
            self.status_bar.setText(f"تم تحميل {len(self.current_templates)} قالب")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل القوالب:\n{e}")
            self.status_bar.setText("خطأ في تحميل القوالب")

    def update_templates_table(self):
        """تحديث جدول القوالب"""
        self.templates_table.setRowCount(len(self.current_templates))

        for row, template in enumerate(self.current_templates):
            # اسم القالب
            name_item = QTableWidgetItem(ArabicSupport.reshape_arabic_text(template.name))
            name_item.setData(Qt.UserRole, template.id)
            self.templates_table.setItem(row, 0, name_item)

            # نوع القالب
            type_item = QTableWidgetItem(ArabicSupport.reshape_arabic_text(template.template_type))
            self.templates_table.setItem(row, 1, type_item)

            # الحالة
            status = "نشط" if template.is_active else "غير نشط"
            status_item = QTableWidgetItem(status)
            if template.is_active:
                status_item.setBackground(Qt.green)
            else:
                status_item.setBackground(Qt.red)
            self.templates_table.setItem(row, 2, status_item)

            # تاريخ الإنشاء
            date_str = template.created_at.strftime("%Y-%m-%d") if template.created_at else ""
            date_item = QTableWidgetItem(date_str)
            self.templates_table.setItem(row, 3, date_item)

    def on_template_selection_changed(self):
        """عند تغيير تحديد القالب"""
        selected_rows = self.templates_table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0

        self.edit_template_btn.setEnabled(has_selection)
        self.delete_template_btn.setEnabled(has_selection)

        if has_selection:
            template = self.get_selected_template()
            if template:
                self.load_template_details(template)

    def get_selected_template(self) -> Optional[LetterTemplate]:
        """الحصول على القالب المحدد"""
        selected_rows = self.templates_table.selectionModel().selectedRows()
        if not selected_rows:
            return None

        row = selected_rows[0].row()
        template_id = self.templates_table.item(row, 0).data(Qt.UserRole)

        for template in self.current_templates:
            if template.id == template_id:
                return template

        return None

    def load_template_details(self, template: LetterTemplate):
        """تحميل تفاصيل القالب"""
        self.template_name_input.setText(template.name)

        # تعيين نوع القالب
        type_index = self.template_type_combo.findText(template.template_type)
        if type_index >= 0:
            self.template_type_combo.setCurrentIndex(type_index)

        self.template_description_input.setPlainText(template.description)

        # عرض مسار الملف
        if template.file_path and os.path.exists(template.file_path):
            file_name = os.path.basename(template.file_path)
            self.template_file_label.setText(f"✅ {file_name}")
            self.template_file_label.setStyleSheet("color: #28a745;")
            self.validate_btn.setEnabled(True)
        else:
            self.template_file_label.setText("❌ ملف غير موجود")
            self.template_file_label.setStyleSheet("color: #dc3545;")
            self.validate_btn.setEnabled(False)

    def add_new_template(self):
        """إضافة قالب جديد"""
        self.clear_template_form()
        self.save_template_btn.setEnabled(False)
        self.status_bar.setText("إضافة قالب جديد...")

    def import_template(self):
        """استيراد قالب من ملف"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختيار ملف القالب",
            str(TEMPLATES_DIR),
            "ملفات Word (*.docx);;جميع الملفات (*)"
        )

        if file_path:
            self.clear_template_form()

            # تعيين مسار الملف
            self.template_file_label.setText(f"✅ {os.path.basename(file_path)}")
            self.template_file_label.setStyleSheet("color: #28a745;")
            self.template_file_label.setProperty("file_path", file_path)

            # تعيين اسم افتراضي
            file_name = Path(file_path).stem
            self.template_name_input.setText(file_name)

            self.validate_btn.setEnabled(True)
            self.validate_form()
            self.status_bar.setText(f"تم اختيار الملف: {os.path.basename(file_path)}")

    def edit_selected_template(self):
        """تعديل القالب المحدد"""
        template = self.get_selected_template()
        if not template:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد قالب للتعديل")
            return

        self.load_template_details(template)
        self.save_template_btn.setEnabled(True)
        self.status_bar.setText(f"تعديل القالب: {template.name}")

    def delete_selected_template(self):
        """حذف القالب المحدد"""
        template = self.get_selected_template()
        if not template:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد قالب للحذف")
            return

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف القالب '{template.name}'؟\n"
            "لن يمكن التراجع عن هذا الإجراء.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # حذف من قاعدة البيانات
                success = self.db_manager.delete_template(template.id)

                if success:
                    # حذف الملف إذا كان موجوداً
                    if template.file_path and os.path.exists(template.file_path):
                        try:
                            os.remove(template.file_path)
                        except:
                            pass  # تجاهل خطأ حذف الملف

                    QMessageBox.information(self, "نجح", "تم حذف القالب بنجاح")
                    self.load_templates()
                    self.clear_template_form()
                    self.template_updated.emit()
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في حذف القالب")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف القالب:\n{e}")

    def choose_template_file(self):
        """اختيار ملف القالب"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختيار ملف القالب",
            str(TEMPLATES_DIR),
            "ملفات Word (*.docx);;جميع الملفات (*)"
        )

        if file_path:
            self.template_file_label.setText(f"✅ {os.path.basename(file_path)}")
            self.template_file_label.setStyleSheet("color: #28a745;")
            self.template_file_label.setProperty("file_path", file_path)
            self.validate_btn.setEnabled(True)
            self.validate_form()

    def validate_template(self):
        """التحقق من صحة القالب"""
        file_path = self.template_file_label.property("file_path")
        if not file_path or not os.path.exists(file_path):
            QMessageBox.warning(self, "تحذير", "يرجى اختيار ملف قالب صحيح أولاً")
            return

        # إظهار شريط التقدم
        self.validation_progress.setVisible(True)
        self.validation_progress.setRange(0, 0)  # شريط تقدم غير محدد
        self.validate_btn.setEnabled(False)

        # بدء التحقق في خيط منفصل
        self.validation_worker = TemplateValidationWorker(file_path)
        self.validation_worker.validation_complete.connect(self.on_validation_complete)
        self.validation_worker.start()

        self.status_bar.setText("جاري فحص القالب...")

    def on_validation_complete(self, result: dict):
        """عند اكتمال التحقق من صحة القالب"""
        # إخفاء شريط التقدم
        self.validation_progress.setVisible(False)
        self.validate_btn.setEnabled(True)

        # عرض النتائج
        if result['valid']:
            result_text = "✅ القالب صحيح ومتوافق\n\n"

            if result['variables_found']:
                result_text += "المتغيرات الموجودة:\n"
                for var in result['variables_found']:
                    result_text += f"  • {var}\n"

            if result['missing_variables']:
                result_text += "\nالمتغيرات المفقودة (اختيارية):\n"
                for var in result['missing_variables']:
                    result_text += f"  • {var}\n"

            self.validation_result.setStyleSheet("color: #28a745;")
            self.status_bar.setText("القالب صحيح ومتوافق")
        else:
            result_text = f"❌ خطأ في القالب:\n{result.get('error', 'خطأ غير معروف')}"
            self.validation_result.setStyleSheet("color: #dc3545;")
            self.status_bar.setText("القالب غير صحيح")

        self.validation_result.setPlainText(result_text)

    def save_template(self):
        """حفظ القالب"""
        if not self.validate_form():
            QMessageBox.warning(self, "خطأ", "يرجى ملء جميع الحقول المطلوبة")
            return

        try:
            # إنشاء كائن القالب
            template = LetterTemplate()
            template.name = self.template_name_input.text().strip()
            template.template_type = self.template_type_combo.currentText()
            template.description = self.template_description_input.toPlainText().strip()

            # نسخ الملف إلى مجلد القوالب
            source_file = self.template_file_label.property("file_path")
            if source_file and os.path.exists(source_file):
                # إنشاء اسم ملف فريد
                file_name = f"{template.name}_{template.template_type}.docx"
                file_name = file_name.replace(" ", "_").replace("/", "_")
                target_file = TEMPLATES_DIR / file_name

                # نسخ الملف
                import shutil
                shutil.copy2(source_file, target_file)
                template.file_path = str(target_file)

            # حفظ في قاعدة البيانات
            template_id = self.db_manager.add_template(template)

            if template_id > 0:
                QMessageBox.information(self, "نجح", "تم حفظ القالب بنجاح")
                self.load_templates()
                self.clear_template_form()
                self.template_updated.emit()
                self.status_bar.setText("تم حفظ القالب بنجاح")
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ القالب")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ القالب:\n{e}")

    def cancel_edit(self):
        """إلغاء التعديل"""
        self.clear_template_form()
        self.status_bar.setText("تم إلغاء التعديل")

    def clear_template_form(self):
        """مسح نموذج القالب"""
        self.template_name_input.clear()
        self.template_type_combo.setCurrentIndex(0)
        self.template_description_input.clear()
        self.template_file_label.setText("لم يتم اختيار ملف")
        self.template_file_label.setStyleSheet("color: #6c757d; font-style: italic;")
        self.template_file_label.setProperty("file_path", None)
        self.validation_result.clear()
        self.validate_btn.setEnabled(False)
        self.save_template_btn.setEnabled(False)

    def validate_form(self):
        """التحقق من صحة النموذج"""
        name = self.template_name_input.text().strip()
        has_file = bool(self.template_file_label.property("file_path"))

        is_valid = bool(name and has_file)
        self.save_template_btn.setEnabled(is_valid)

        return is_valid
