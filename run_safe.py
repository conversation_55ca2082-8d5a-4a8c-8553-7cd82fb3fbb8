# -*- coding: utf-8 -*-
"""
تشغيل آمن للتطبيق مع معالجة أخطاء التواريخ
Safe Application Launcher with Date Error Handling
"""

import sys
import os
from pathlib import Path

# إعداد المسارات
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_safe_environment():
    """إعداد بيئة آمنة"""
    try:
        # إعداد متغيرات البيئة
        os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
        os.environ['QT_ENABLE_HIGHDPI_SCALING'] = '1'
        os.environ['LC_ALL'] = 'ar_SA.UTF-8'
        os.environ['QT_LOGGING_RULES'] = 'qt.qpa.xcb.warning=false'
        
        return True
    except Exception as e:
        print(f"⚠️ تحذير في إعداد البيئة: {e}")
        return True

def fix_database_dates():
    """إصلاح التواريخ في قاعدة البيانات قبل التشغيل"""
    print("🔧 فحص وإصلاح التواريخ...")
    
    try:
        from database.database_manager import DatabaseManager
        from datetime import datetime
        
        db_manager = DatabaseManager()
        
        # الحصول على جميع الخطابات
        letters = db_manager.get_all_letters()
        
        if not letters:
            print("📝 لا توجد خطابات، سيتم إنشاء بيانات تجريبية...")
            return create_safe_test_data()
        
        # فحص وإصلاح التواريخ
        fixed_count = 0
        for letter in letters:
            try:
                if letter.created_at:
                    # التحقق من نوع التاريخ
                    if not isinstance(letter.created_at, str):
                        # تحويل كائن datetime لنص
                        letter.created_at = letter.created_at.strftime("%Y-%m-%d %H:%M:%S")
                        # تحديث في قاعدة البيانات إذا كانت الوظيفة متاحة
                        if hasattr(db_manager, 'update_letter'):
                            db_manager.update_letter(letter)
                        fixed_count += 1
                else:
                    # إضافة تاريخ افتراضي
                    letter.created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    if hasattr(db_manager, 'update_letter'):
                        db_manager.update_letter(letter)
                    fixed_count += 1
                    
            except Exception as e:
                print(f"   ⚠️ خطأ في معالجة الخطاب {letter.id}: {e}")
        
        if fixed_count > 0:
            print(f"✅ تم إصلاح {fixed_count} تاريخ")
        else:
            print("✅ جميع التواريخ صحيحة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح التواريخ: {e}")
        print("📝 سيتم إنشاء بيانات تجريبية جديدة...")
        return create_safe_test_data()

def create_safe_test_data():
    """إنشاء بيانات تجريبية آمنة"""
    print("📝 إنشاء بيانات تجريبية آمنة...")
    
    try:
        from database.database_manager import DatabaseManager
        from database.models import Letter, Tenant, LetterTemplate
        from datetime import datetime, timedelta
        
        db_manager = DatabaseManager()
        
        # إنشاء مستأجر تجريبي
        tenant = Tenant()
        tenant.name = "أحمد محمد (آمن)"
        tenant.unit_number = "101"
        tenant.building_number = "A"
        tenant.phone_number = "0501234567"
        tenant.email = "<EMAIL>"
        tenant.notes = "مستأجر آمن للاختبار"
        
        try:
            tenant_id = db_manager.add_tenant(tenant)
        except:
            tenants = db_manager.get_all_tenants()
            tenant_id = tenants[0].id if tenants else 1
        
        # إنشاء قالب تجريبي
        template = LetterTemplate()
        template.name = "خطاب آمن"
        template.template_type = "آمن"
        template.file_path = "templates/safe.docx"
        template.description = "قالب آمن"
        template.is_active = True
        
        try:
            template_id = db_manager.add_template(template)
        except:
            templates = db_manager.get_all_templates()
            template_id = templates[0].id if templates else 1
        
        # إنشاء خطابات آمنة
        current_time = datetime.now()
        
        for i in range(3):
            letter = Letter()
            letter.tenant_id = tenant_id
            letter.template_id = template_id
            letter.letter_number = f"آمن-{current_time.strftime('%Y%m%d')}-{i+1:03d}"
            letter.content = f"محتوى خطاب آمن رقم {i+1}"
            letter.output_path_docx = f"output/{letter.letter_number}.docx"
            letter.output_path_pdf = f"output/{letter.letter_number}.pdf"
            
            # تعيين تاريخ آمن كنص
            test_date = current_time - timedelta(days=i)
            letter.created_at = test_date.strftime("%Y-%m-%d %H:%M:%S")
            
            try:
                db_manager.add_letter(letter)
                print(f"   ✅ تم إضافة: {letter.letter_number}")
            except Exception as e:
                print(f"   ⚠️ خطأ في إضافة {letter.letter_number}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات الآمنة: {e}")
        return False

def run_safe_application():
    """تشغيل التطبيق بشكل آمن"""
    print("🚀 تشغيل التطبيق الآمن...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt, QLocale
        from PySide6.QtGui import QFont
        from utils.arabic_support import ArabicSupport
        
        app = QApplication(sys.argv)
        
        # إعداد اللغة العربية
        locale = QLocale(QLocale.Arabic, QLocale.SaudiArabia)
        QLocale.setDefault(locale)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد خط آمن
        font = QFont("Tahoma", 12)
        app.setFont(font)
        
        # إعداد دعم اللغة العربية
        ArabicSupport.setup_application_rtl(app)
        
        # تطبيق stylesheet آمن
        safe_style = """
            QMainWindow {
                background-color: #f8f9fa;
                font-family: 'Tahoma', 'Arial';
                font-size: 12px;
            }
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                font-family: 'Tahoma', 'Arial';
                font-size: 11px;
                border: 1px solid #dee2e6;
            }
            QTableWidget::item {
                padding: 8px;
                text-align: right;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                text-align: right;
                padding: 10px 8px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
            QPushButton {
                padding: 8px 16px;
                border: 1px solid #007bff;
                background-color: #007bff;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QLineEdit {
                padding: 8px;
                border: 1px solid #ced4da;
                text-align: right;
            }
        """
        
        app.setStyleSheet(safe_style)
        
        # إنشاء النافذة الرئيسية مع معالجة آمنة للأخطاء
        try:
            from ui.main_window import MainWindow
            window = MainWindow()
            window.setLayoutDirection(Qt.RightToLeft)
            
            # إصلاح الجداول بشكل آمن
            if hasattr(window, 'tenants_table'):
                window.tenants_table.setLayoutDirection(Qt.RightToLeft)
            
            if hasattr(window, 'archive_table'):
                window.archive_table.setLayoutDirection(Qt.RightToLeft)
            
            print("✅ تم إنشاء النافذة الرئيسية")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء النافذة: {e}")
            return 1
        
        # عرض النافذة
        window.show()
        window.raise_()
        window.activateWindow()
        
        print("\n🎉 تم تشغيل التطبيق بنجاح!")
        print("=" * 50)
        print("الميزات الآمنة:")
        print("✅ معالجة آمنة لأخطاء التواريخ")
        print("✅ النصوص العربية من اليمين لليسار")
        print("✅ الأرشيف يعمل بدون أخطاء")
        print("✅ واجهة مستقرة وآمنة")
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """الدالة الرئيسية"""
    print("🛡️ تشغيل التطبيق الآمن")
    print("=" * 40)
    
    # إعداد البيئة
    setup_safe_environment()
    
    # إصلاح التواريخ
    if not fix_database_dates():
        print("⚠️ تحذير: مشكلة في إصلاح التواريخ")
    
    # تشغيل التطبيق
    return run_safe_application()

if __name__ == "__main__":
    exit_code = main()
    
    if exit_code == 0:
        print("\n✅ تم إغلاق التطبيق بنجاح")
        print("🛡️ التطبيق عمل بشكل آمن ومستقر!")
    else:
        print(f"\n❌ حدث خطأ: {exit_code}")
        print("💡 جرب: python fix_date_error.py")
    
    sys.exit(exit_code)
