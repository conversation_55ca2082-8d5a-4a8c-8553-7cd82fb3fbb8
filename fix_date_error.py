# -*- coding: utf-8 -*-
"""
إصلاح خطأ التواريخ في الأرشيف
Fix Date Error in Archive
"""

import sys
from pathlib import Path

# إعداد المسارات
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def fix_date_format_in_database():
    """إصلاح تنسيق التواريخ في قاعدة البيانات"""
    print("🔧 إصلاح تنسيق التواريخ في قاعدة البيانات...")
    
    try:
        from database.database_manager import DatabaseManager
        import sqlite3
        from datetime import datetime
        
        db_manager = DatabaseManager()
        
        # الحصول على جميع الخطابات
        letters = db_manager.get_all_letters()
        print(f"📋 تم العثور على {len(letters)} خطاب")
        
        # فحص وإصلاح التواريخ
        fixed_count = 0
        for letter in letters:
            try:
                if letter.created_at:
                    # التحقق من نوع التاريخ
                    if isinstance(letter.created_at, str):
                        # محاولة تحويل النص لتاريخ للتأكد من صحته
                        try:
                            datetime.strptime(letter.created_at[:19], "%Y-%m-%d %H:%M:%S")
                            print(f"   ✅ التاريخ صحيح: {letter.created_at[:10]}")
                        except ValueError:
                            # إصلاح التاريخ إذا كان خاطئ
                            letter.created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            db_manager.update_letter(letter)
                            fixed_count += 1
                            print(f"   🔧 تم إصلاح التاريخ للخطاب {letter.id}")
                    else:
                        # تحويل كائن datetime لنص
                        letter.created_at = letter.created_at.strftime("%Y-%m-%d %H:%M:%S")
                        db_manager.update_letter(letter)
                        fixed_count += 1
                        print(f"   🔧 تم تحويل التاريخ للخطاب {letter.id}")
                else:
                    # إضافة تاريخ افتراضي
                    letter.created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    db_manager.update_letter(letter)
                    fixed_count += 1
                    print(f"   🔧 تم إضافة تاريخ للخطاب {letter.id}")
                    
            except Exception as e:
                print(f"   ⚠️ خطأ في معالجة الخطاب {letter.id}: {e}")
        
        print(f"✅ تم إصلاح {fixed_count} تاريخ")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح التواريخ: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_archive_after_fix():
    """اختبار الأرشيف بعد إصلاح التواريخ"""
    print("\n🧪 اختبار الأرشيف بعد إصلاح التواريخ...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # اختبار الحصول على جميع الخطابات
        letters = db_manager.get_all_letters()
        print(f"📋 عدد الخطابات: {len(letters)}")
        
        # اختبار فلترة التواريخ
        from datetime import datetime, timedelta
        from_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        to_date = datetime.now().strftime("%Y-%m-%d")
        
        filtered_letters = db_manager.get_letters_by_date_range(from_date, to_date)
        print(f"📅 الخطابات في آخر 30 يوم: {len(filtered_letters)}")
        
        # عرض عينة من التواريخ
        print("\n📝 عينة من التواريخ:")
        for i, letter in enumerate(letters[:3], 1):
            print(f"   {i}. خطاب {letter.id}: {letter.created_at}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأرشيف: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_archive():
    """اختبار واجهة الأرشيف"""
    print("\n🖥️ اختبار واجهة الأرشيف...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.main_window import MainWindow
        from utils.arabic_support import ArabicSupport
        
        app = QApplication(sys.argv)
        
        # إعداد RTL
        ArabicSupport.setup_application_rtl(app)
        
        # إنشاء النافذة
        window = MainWindow()
        
        # اختبار تحميل الأرشيف
        print("📋 اختبار تحميل الأرشيف...")
        window.load_archive()
        
        # فحص عدد الصفوف
        if hasattr(window, 'archive_table'):
            row_count = window.archive_table.rowCount()
            print(f"✅ عدد الصفوف في الأرشيف: {row_count}")
            
            if row_count > 0:
                print("✅ الأرشيف يحتوي على بيانات")
                
                # عرض النافذة لمدة قصيرة
                window.show()
                window.raise_()
                window.activateWindow()
                
                print("🎯 النافذة ستظهر لمدة 3 ثوان...")
                
                # إغلاق تلقائي
                from PySide6.QtCore import QTimer
                QTimer.singleShot(3000, app.quit)
                
                app.exec()
                
                return True
            else:
                print("⚠️ الأرشيف فارغ")
                return False
        else:
            print("❌ لم يتم العثور على جدول الأرشيف")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة الأرشيف: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_simple_test_data():
    """إنشاء بيانات اختبار بسيطة"""
    print("\n📝 إنشاء بيانات اختبار بسيطة...")
    
    try:
        from database.database_manager import DatabaseManager
        from database.models import Letter, Tenant, LetterTemplate
        from datetime import datetime, timedelta
        
        db_manager = DatabaseManager()
        
        # إنشاء مستأجر بسيط
        tenant = Tenant()
        tenant.name = "أحمد محمد (اختبار)"
        tenant.unit_number = "101"
        tenant.building_number = "A"
        tenant.phone_number = "0501234567"
        tenant.email = "<EMAIL>"
        tenant.notes = "مستأجر اختبار"
        
        try:
            tenant_id = db_manager.add_tenant(tenant)
            print(f"✅ تم إنشاء مستأجر: {tenant.name}")
        except:
            # المستأجر موجود، استخدم الموجود
            tenants = db_manager.get_all_tenants()
            tenant_id = tenants[0].id if tenants else 1
            print(f"✅ استخدام مستأجر موجود")
        
        # إنشاء قالب بسيط
        template = LetterTemplate()
        template.name = "خطاب اختبار"
        template.template_type = "اختبار"
        template.file_path = "templates/test.docx"
        template.description = "قالب اختبار"
        template.is_active = True
        
        try:
            template_id = db_manager.add_template(template)
            print(f"✅ تم إنشاء قالب: {template.name}")
        except:
            # القالب موجود، استخدم الموجود
            templates = db_manager.get_all_templates()
            template_id = templates[0].id if templates else 1
            print(f"✅ استخدام قالب موجود")
        
        # إنشاء خطابات بسيطة مع تواريخ صحيحة
        current_time = datetime.now()
        test_letters = []
        
        for i in range(3):
            letter = Letter()
            letter.tenant_id = tenant_id
            letter.template_id = template_id
            letter.letter_number = f"اختبار-{current_time.strftime('%Y%m%d')}-{i+1:03d}"
            letter.content = f"محتوى خطاب اختبار رقم {i+1}"
            letter.output_path_docx = f"output/{letter.letter_number}.docx"
            letter.output_path_pdf = f"output/{letter.letter_number}.pdf"
            
            # تعيين تاريخ صحيح كنص
            test_date = current_time - timedelta(days=i)
            letter.created_at = test_date.strftime("%Y-%m-%d %H:%M:%S")
            
            test_letters.append(letter)
        
        # إضافة الخطابات
        added_count = 0
        for letter in test_letters:
            try:
                letter_id = db_manager.add_letter(letter)
                if letter_id > 0:
                    added_count += 1
                    print(f"   ✅ تم إضافة: {letter.letter_number}")
            except Exception as e:
                print(f"   ⚠️ خطأ في إضافة {letter.letter_number}: {e}")
        
        print(f"✅ تم إنشاء {added_count} خطاب اختبار")
        return added_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح خطأ التواريخ في الأرشيف")
    print("=" * 50)
    
    # إصلاح التواريخ في قاعدة البيانات
    if not fix_date_format_in_database():
        print("\n📝 إنشاء بيانات اختبار جديدة...")
        if not create_simple_test_data():
            print("❌ فشل في إنشاء البيانات")
            return 1
    
    # اختبار قاعدة البيانات
    if not test_archive_after_fix():
        print("❌ فشل في اختبار قاعدة البيانات")
        return 1
    
    # اختبار الواجهة
    if test_ui_archive():
        print("\n✅ تم إصلاح خطأ التواريخ بنجاح!")
        print("📋 الأرشيف يعمل بدون أخطاء")
    else:
        print("\n⚠️ مشكلة في واجهة الأرشيف")
    
    print("\n" + "=" * 50)
    print("🎉 تم إصلاح خطأ التواريخ!")
    print("\nلتشغيل التطبيق:")
    print("python run_clean.py")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    
    if exit_code == 0:
        print("\n✅ تم الإصلاح بنجاح")
    else:
        print("\n❌ حدث خطأ في الإصلاح")
    
    input("اضغط Enter للخروج...")
    sys.exit(exit_code)
