# -*- coding: utf-8 -*-
"""
اختبار واجهة المستخدم الرسومية
GUI Test
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget, QPushButton, QMessageBox
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

class TestWindow(QMainWindow):
    """نافذة اختبار بسيطة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("اختبار نظام إدارة خطابات المستأجرين")
        self.setGeometry(100, 100, 600, 400)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(central_widget)
        
        # العنوان
        title_label = QLabel("نظام إدارة خطابات المستأجرين")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont("Tahoma", 16, QFont.Bold)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # رسالة الحالة
        status_label = QLabel("✅ النظام يعمل بنجاح!")
        status_label.setAlignment(Qt.AlignCenter)
        status_font = QFont("Tahoma", 12)
        status_label.setFont(status_font)
        layout.addWidget(status_label)
        
        # معلومات النظام
        info_label = QLabel("""
📋 المميزات المتاحة:
• إدارة المستأجرين
• إنشاء الخطابات
• دعم اللغة العربية
• إرسال واتساب
• قاعدة بيانات محلية
        """)
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setFont(status_font)
        layout.addWidget(info_label)
        
        # أزرار الاختبار
        test_btn = QPushButton("اختبار رسالة")
        test_btn.setFont(status_font)
        test_btn.clicked.connect(self.show_test_message)
        layout.addWidget(test_btn)
        
        main_app_btn = QPushButton("تشغيل التطبيق الرئيسي")
        main_app_btn.setFont(status_font)
        main_app_btn.clicked.connect(self.launch_main_app)
        layout.addWidget(main_app_btn)
        
        # إعداد اتجاه RTL
        self.setLayoutDirection(Qt.RightToLeft)
    
    def show_test_message(self):
        """عرض رسالة اختبار"""
        QMessageBox.information(
            self, "اختبار", 
            "✅ واجهة المستخدم تعمل بنجاح!\n\nيمكنك الآن تشغيل التطبيق الرئيسي."
        )
    
    def launch_main_app(self):
        """تشغيل التطبيق الرئيسي"""
        try:
            self.close()
            import main
            # سيتم تشغيل التطبيق الرئيسي
        except Exception as e:
            QMessageBox.critical(
                self, "خطأ", 
                f"فشل في تشغيل التطبيق الرئيسي:\n\n{e}"
            )

def main():
    """الدالة الرئيسية"""
    try:
        print("🚀 بدء اختبار واجهة المستخدم...")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد الخط الافتراضي
        font = QFont("Tahoma", 10)
        app.setFont(font)
        
        # إعداد اتجاه RTL
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء النافذة
        window = TestWindow()
        window.show()
        
        print("✅ تم إنشاء النافذة بنجاح")
        print("📝 إذا لم تظهر النافذة، تحقق من شريط المهام")
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة المستخدم: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
