# دليل استكشاف الأخطاء - نظام إدارة خطابات المستأجرين

## 🔍 المشاكل الشائعة وحلولها

### 1. البرنامج لا يبدأ أو لا تظهر النافذة

#### الأعراض:
- البرنامج يبدأ ولكن لا تظهر النافذة
- رسالة "تم تشغيل التطبيق بنجاح" تظهر ولكن لا نافذة

#### الحلول:

**أ) تحقق من شريط المهام:**
- ابحث عن أيقونة البرنامج في شريط المهام السفلي
- انقر عليها إذا وجدتها

**ب) تشغيل الملف المحسن:**
```bash
python start_app.py
```

**ج) اختبار واجهة المستخدم:**
```bash
python test_gui.py
```

**د) إعادة تشغيل النظام:**
- أعد تشغيل الكمبيوتر وحاول مرة أخرى

### 2. خطأ في المكتبات المطلوبة

#### الأعراض:
- رسالة "ModuleNotFoundError"
- رسالة "مكتبات مفقودة"

#### الحلول:

**أ) تثبيت المكتبات يدوياً:**
```bash
pip install PySide6
pip install python-docx
pip install arabic-reshaper
pip install python-bidi
pip install Pillow
```

**ب) تثبيت من ملف المتطلبات:**
```bash
pip install -r requirements.txt
```

**ج) تحديث pip:**
```bash
python -m pip install --upgrade pip
pip install -r requirements.txt
```

### 3. مشاكل في عرض النصوص العربية

#### الأعراض:
- النصوص العربية تظهر مقطعة أو بشكل خاطئ
- الاتجاه من اليسار لليمين بدلاً من اليمين لليسار

#### الحلول:

**أ) تثبيت خط Tahoma:**
- تأكد من وجود خط Tahoma في النظام
- يمكن تحميله من موقع Microsoft

**ب) تثبيت مكتبات اللغة العربية:**
```bash
pip install arabic-reshaper python-bidi
```

### 4. فشل في إنشاء ملفات PDF

#### الأعراض:
- رسالة خطأ عند محاولة إنشاء PDF
- الخطابات تُنشأ بصيغة Word فقط

#### الحلول:

**أ) تثبيت Microsoft Word:**
- تأكد من تثبيت Microsoft Word على النظام

**ب) تثبيت LibreOffice (بديل مجاني):**
- حمّل وثبّت LibreOffice من الموقع الرسمي

**ج) تثبيت مكتبة التحويل:**
```bash
pip install docx2pdf
```

### 5. مشاكل في قاعدة البيانات

#### الأعراض:
- خطأ "database is locked"
- فقدان البيانات المحفوظة

#### الحلول:

**أ) إغلاق جميع نسخ البرنامج:**
- تأكد من عدم تشغيل أكثر من نسخة واحدة

**ب) حذف ملف قاعدة البيانات المعطل:**
```bash
# احذف الملف التالي (ستفقد البيانات):
data/tenants.db
```

**ج) استعادة نسخة احتياطية:**
- انسخ ملف قاعدة البيانات من النسخة الاحتياطية

### 6. مشاكل في إرسال واتساب

#### الأعراض:
- لا يفتح واتساب ويب
- رسالة خطأ في رقم الهاتف

#### الحلول:

**أ) تحقق من تنسيق رقم الهاتف:**
- استخدم الصيغة: 0501234567
- أو: +966501234567

**ب) تحقق من الاتصال بالإنترنت:**
- تأكد من وجود اتصال إنترنت نشط

**ج) تحديث المتصفح:**
- تأكد من تحديث المتصفح الافتراضي

## 🛠️ أدوات التشخيص

### 1. اختبار النظام الكامل:
```bash
python test_app.py
```

### 2. اختبار واجهة المستخدم:
```bash
python test_gui.py
```

### 3. تشغيل مع تشخيص مفصل:
```bash
python debug_main.py
```

### 4. تشغيل محسن:
```bash
python start_app.py
```

## 📋 معلومات النظام المطلوبة

### الحد الأدنى:
- **نظام التشغيل**: Windows 7 أو أحدث
- **Python**: 3.8 أو أحدث
- **الذاكرة**: 2 جيجابايت RAM
- **المساحة**: 500 ميجابايت مساحة فارغة

### المستحسن:
- **نظام التشغيل**: Windows 10 أو أحدث
- **Python**: 3.9 أو أحدث
- **الذاكرة**: 4 جيجابايت RAM
- **المساحة**: 1 جيجابايت مساحة فارغة

## 📞 الحصول على المساعدة

### 1. ملفات السجل:
- **السجل الرئيسي**: `logs/app.log`
- **سجل التشغيل**: `logs/startup.log`

### 2. معلومات مفيدة للدعم:
- إصدار Python: `python --version`
- إصدار النظام: Windows version
- رسالة الخطأ الكاملة
- الخطوات التي أدت للمشكلة

### 3. إعادة تثبيت كاملة:
```bash
# 1. حذف المجلدات التالية:
logs/
data/
output/

# 2. إعادة تثبيت المكتبات:
pip uninstall PySide6 python-docx arabic-reshaper python-bidi
pip install -r requirements.txt

# 3. تشغيل البرنامج:
python start_app.py
```

## ✅ نصائح للوقاية

1. **النسخ الاحتياطية**: انسخ مجلد `data/` بانتظام
2. **التحديثات**: حدّث Python والمكتبات دورياً
3. **مساحة القرص**: تأكد من وجود مساحة كافية
4. **الصلاحيات**: شغّل البرنامج بصلاحيات المدير إذا لزم الأمر
5. **مكافح الفيروسات**: أضف مجلد البرنامج للاستثناءات

---

**💡 تذكر**: معظم المشاكل تُحل بإعادة تشغيل البرنامج أو الكمبيوتر!
