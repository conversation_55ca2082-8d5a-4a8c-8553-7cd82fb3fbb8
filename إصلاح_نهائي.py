# -*- coding: utf-8 -*-
"""
إصلاح نهائي لمشكلة النصوص العربية
Final Fix for Arabic Text Issues
"""

import sys
from pathlib import Path

# إعداد المسارات
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def apply_direct_fix():
    """تطبيق إصلاح مباشر للنصوص العربية"""
    print("🔧 تطبيق إصلاح مباشر للنصوص العربية...")
    
    try:
        # قراءة ملف النافذة الرئيسية
        main_window_file = project_root / "ui" / "main_window.py"
        
        if not main_window_file.exists():
            print("❌ ملف النافذة الرئيسية غير موجود")
            return False
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من وجود الإصلاحات
        if "تحديث جدول المستأجرين مع إصلاح كامل للنصوص العربية" in content:
            print("✅ الإصلاحات موجودة بالفعل")
            return True
        else:
            print("⚠️ الإصلاحات غير مطبقة بالكامل")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص الإصلاحات: {e}")
        return False

def test_arabic_support():
    """اختبار دعم اللغة العربية"""
    print("🔤 اختبار دعم اللغة العربية...")
    
    try:
        from utils.arabic_support import ArabicSupport
        
        # اختبار تشكيل النصوص
        test_texts = [
            "أحمد محمد",
            "فاطمة علي", 
            "محمد عبدالله",
            "مستأجر جديد",
            "ملاحظات مهمة"
        ]
        
        print("📝 اختبار تشكيل النصوص:")
        for text in test_texts:
            reshaped = ArabicSupport.reshape_arabic_text(text)
            print(f"   '{text}' -> '{reshaped}'")
        
        print("✅ تشكيل النصوص يعمل بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دعم العربية: {e}")
        return False

def create_test_data():
    """إنشاء بيانات اختبار عربية"""
    print("📝 إنشاء بيانات اختبار عربية...")
    
    try:
        from database.database_manager import DatabaseManager
        from database.models import Tenant
        
        db_manager = DatabaseManager()
        
        # بيانات اختبار عربية
        test_tenants = [
            {
                "name": "أحمد محمد العلي",
                "unit_number": "101",
                "building_number": "A",
                "phone_number": "0501234567",
                "email": "<EMAIL>",
                "notes": "مستأجر جديد"
            },
            {
                "name": "فاطمة عبدالرحمن",
                "unit_number": "205", 
                "building_number": "B",
                "phone_number": "0509876543",
                "email": "<EMAIL>",
                "notes": "مستأجرة قديمة"
            },
            {
                "name": "محمد عبدالله الأحمد",
                "unit_number": "310",
                "building_number": "C", 
                "phone_number": "0551122334",
                "email": "<EMAIL>",
                "notes": "تم التجديد"
            }
        ]
        
        added_count = 0
        for tenant_data in test_tenants:
            # التحقق من عدم وجود المستأجر
            existing = db_manager.search_tenants(tenant_data["name"])
            if not existing:
                tenant = Tenant()
                tenant.name = tenant_data["name"]
                tenant.unit_number = tenant_data["unit_number"]
                tenant.building_number = tenant_data["building_number"]
                tenant.phone_number = tenant_data["phone_number"]
                tenant.email = tenant_data["email"]
                tenant.notes = tenant_data["notes"]
                
                if db_manager.add_tenant(tenant) > 0:
                    added_count += 1
        
        print(f"✅ تم إضافة {added_count} مستأجر تجريبي")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات: {e}")
        return False

def run_application():
    """تشغيل التطبيق مع الإصلاحات"""
    print("🚀 تشغيل التطبيق مع الإصلاحات...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        from utils.arabic_support import ArabicSupport
        from ui.main_window import MainWindow
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد RTL كامل
        ArabicSupport.setup_application_rtl(app)
        
        # إنشاء النافذة
        window = MainWindow()
        window.setLayoutDirection(Qt.RightToLeft)
        
        # عرض النافذة
        window.show()
        window.raise_()
        window.activateWindow()
        
        print("✅ تم تشغيل التطبيق بنجاح")
        print("📋 تحقق من الجدول - النصوص العربية يجب أن تظهر بشكل صحيح")
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return 1

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح نهائي لمشكلة النصوص العربية")
    print("=" * 50)
    
    # فحص الإصلاحات
    if not apply_direct_fix():
        print("❌ فشل في فحص الإصلاحات")
        return 1
    
    # اختبار دعم العربية
    if not test_arabic_support():
        print("❌ فشل في اختبار دعم العربية")
        return 1
    
    # إنشاء بيانات اختبار
    create_test_data()
    
    print("\n" + "=" * 50)
    print("🎉 تم تطبيق جميع الإصلاحات بنجاح!")
    print("\nالإصلاحات المطبقة:")
    print("✅ تشكيل النصوص العربية")
    print("✅ RTL كامل للجداول")
    print("✅ خطوط عربية محسنة")
    print("✅ محاذاة صحيحة")
    print("✅ بيانات اختبار عربية")
    
    print("\n🚀 تشغيل التطبيق...")
    return run_application()

if __name__ == "__main__":
    exit_code = main()
    
    if exit_code == 0:
        print("\n✅ تم إغلاق التطبيق بنجاح")
        print("🔤 النصوص العربية تعمل بشكل مثالي!")
    else:
        print(f"\n❌ حدث خطأ: {exit_code}")
    
    input("اضغط Enter للخروج...")
    sys.exit(exit_code)
