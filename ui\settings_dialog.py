# -*- coding: utf-8 -*-
"""
نافذة إعدادات التطبيق
Application Settings Dialog
"""

import json
import os
from pathlib import Path
from typing import Dict, Any

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit,
    QPushButton, QLabel, QMessageBox, QGroupBox, QComboBox,
    QSpinBox, QCheckBox, QTabWidget, QWidget, QFileDialog,
    QTextEdit, QSlider, QFrame
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QFontDatabase

from utils.arabic_support import ArabicSupport
from config.settings import BASE_DIR, APP_NAME


class SettingsDialog(QDialog):
    """نافذة إعدادات التطبيق"""

    settings_changed = Signal(dict)  # إشارة عند تغيير الإعدادات

    def __init__(self, parent=None):
        super().__init__(parent)
        self.settings_file = BASE_DIR / "config" / "user_settings.json"
        self.current_settings = {}

        self.init_ui()
        self.setup_connections()
        self.load_settings()

        # إعداد دعم اللغة العربية
        ArabicSupport.setup_arabic_font(self, 12)
        ArabicSupport.setup_rtl_layout(self)

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle(f"إعدادات {APP_NAME}")
        self.setModal(True)
        self.resize(700, 600)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)

        # عنوان النافذة
        title_label = QLabel("إعدادات التطبيق")
        title_label.setFont(QFont("Tahoma", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                padding: 15px;
                background-color: #007bff;
                color: white;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)

        # التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 5px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        main_layout.addWidget(self.tab_widget)

        # إنشاء التبويبات
        self.create_general_tab()
        self.create_appearance_tab()
        self.create_letters_tab()
        self.create_advanced_tab()

        # أزرار التحكم
        self.create_control_buttons(main_layout)

    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        general_widget = QWidget()
        layout = QVBoxLayout(general_widget)

        # إعدادات التطبيق
        app_group = QGroupBox("إعدادات التطبيق")
        app_layout = QFormLayout(app_group)

        self.auto_save_check = QCheckBox("حفظ تلقائي للبيانات")
        self.auto_save_check.setChecked(True)
        app_layout.addRow("الحفظ التلقائي:", self.auto_save_check)

        self.backup_check = QCheckBox("إنشاء نسخ احتياطية تلقائية")
        self.backup_check.setChecked(True)
        app_layout.addRow("النسخ الاحتياطية:", self.backup_check)

        self.startup_check = QCheckBox("تشغيل مع بداية النظام")
        self.startup_check.setChecked(False)
        app_layout.addRow("التشغيل التلقائي:", self.startup_check)

        layout.addWidget(app_group)

        # إعدادات المجلدات
        folders_group = QGroupBox("مجلدات التطبيق")
        folders_layout = QFormLayout(folders_group)

        self.templates_folder_input = QLineEdit()
        self.templates_folder_input.setReadOnly(True)
        templates_btn = QPushButton("تغيير")
        templates_btn.clicked.connect(lambda: self.choose_folder(self.templates_folder_input))

        templates_layout_h = QHBoxLayout()
        templates_layout_h.addWidget(self.templates_folder_input)
        templates_layout_h.addWidget(templates_btn)
        folders_layout.addRow("مجلد القوالب:", templates_layout_h)

        self.output_folder_input = QLineEdit()
        self.output_folder_input.setReadOnly(True)
        output_btn = QPushButton("تغيير")
        output_btn.clicked.connect(lambda: self.choose_folder(self.output_folder_input))

        output_layout_h = QHBoxLayout()
        output_layout_h.addWidget(self.output_folder_input)
        output_layout_h.addWidget(output_btn)
        folders_layout.addRow("مجلد الخطابات:", output_layout_h)

        layout.addWidget(folders_group)

        # إعدادات اللغة والمنطقة
        language_group = QGroupBox("اللغة والمنطقة")
        language_layout = QFormLayout(language_group)

        self.language_combo = QComboBox()
        self.language_combo.addItems(["العربية", "English"])
        language_layout.addRow("لغة الواجهة:", self.language_combo)

        self.date_format_combo = QComboBox()
        self.date_format_combo.addItems([
            "DD/MM/YYYY", "MM/DD/YYYY", "YYYY-MM-DD"
        ])
        language_layout.addRow("تنسيق التاريخ:", self.date_format_combo)

        layout.addWidget(language_group)
        layout.addStretch()

        self.tab_widget.addTab(general_widget, "عام")

    def create_appearance_tab(self):
        """إنشاء تبويب المظهر"""
        appearance_widget = QWidget()
        layout = QVBoxLayout(appearance_widget)

        # إعدادات الخطوط
        font_group = QGroupBox("إعدادات الخطوط")
        font_layout = QFormLayout(font_group)

        self.font_family_combo = QComboBox()
        self.font_family_combo.setEditable(True)
        # إضافة الخطوط المتاحة
        font_db = QFontDatabase()
        fonts = font_db.families()
        arabic_fonts = [font for font in fonts if any(char in font.lower() for char in ['arabic', 'tahoma', 'arial'])]
        self.font_family_combo.addItems(arabic_fonts[:10])  # أول 10 خطوط عربية
        font_layout.addRow("نوع الخط:", self.font_family_combo)

        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setValue(12)
        font_layout.addRow("حجم الخط:", self.font_size_spin)

        self.bold_check = QCheckBox("خط عريض")
        font_layout.addRow("نمط الخط:", self.bold_check)

        layout.addWidget(font_group)

        # إعدادات الألوان
        colors_group = QGroupBox("إعدادات الألوان")
        colors_layout = QFormLayout(colors_group)

        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["فاتح", "داكن", "تلقائي"])
        colors_layout.addRow("المظهر:", self.theme_combo)

        self.accent_color_combo = QComboBox()
        self.accent_color_combo.addItems([
            "أزرق", "أخضر", "أحمر", "برتقالي", "بنفسجي"
        ])
        colors_layout.addRow("اللون المميز:", self.accent_color_combo)

        layout.addWidget(colors_group)

        # إعدادات النافذة
        window_group = QGroupBox("إعدادات النافذة")
        window_layout = QFormLayout(window_group)

        self.window_width_spin = QSpinBox()
        self.window_width_spin.setRange(800, 2000)
        self.window_width_spin.setValue(1200)
        window_layout.addRow("عرض النافذة:", self.window_width_spin)

        self.window_height_spin = QSpinBox()
        self.window_height_spin.setRange(600, 1500)
        self.window_height_spin.setValue(800)
        window_layout.addRow("ارتفاع النافذة:", self.window_height_spin)

        self.maximize_check = QCheckBox("تكبير النافذة عند البدء")
        window_layout.addRow("تكبير تلقائي:", self.maximize_check)

        layout.addWidget(window_group)
        layout.addStretch()

        self.tab_widget.addTab(appearance_widget, "المظهر")

    def create_letters_tab(self):
        """إنشاء تبويب إعدادات الخطابات"""
        letters_widget = QWidget()
        layout = QVBoxLayout(letters_widget)

        # إعدادات الخطابات
        letters_group = QGroupBox("إعدادات الخطابات")
        letters_layout = QFormLayout(letters_group)

        self.letter_prefix_input = QLineEdit()
        self.letter_prefix_input.setText("خطاب-")
        letters_layout.addRow("بادئة رقم الخطاب:", self.letter_prefix_input)

        self.auto_number_check = QCheckBox("ترقيم تلقائي للخطابات")
        self.auto_number_check.setChecked(True)
        letters_layout.addRow("الترقيم التلقائي:", self.auto_number_check)

        self.auto_pdf_check = QCheckBox("إنشاء PDF تلقائياً")
        self.auto_pdf_check.setChecked(True)
        letters_layout.addRow("PDF تلقائي:", self.auto_pdf_check)

        layout.addWidget(letters_group)

        # إعدادات القوالب
        templates_group = QGroupBox("إعدادات القوالب")
        templates_layout = QFormLayout(templates_group)

        self.default_template_combo = QComboBox()
        self.default_template_combo.addItem("قالب افتراضي")
        templates_layout.addRow("القالب الافتراضي:", self.default_template_combo)

        self.template_validation_check = QCheckBox("فحص القوالب تلقائياً")
        self.template_validation_check.setChecked(True)
        templates_layout.addRow("فحص تلقائي:", self.template_validation_check)

        layout.addWidget(templates_group)

        # إعدادات واتساب
        whatsapp_group = QGroupBox("إعدادات واتساب")
        whatsapp_layout = QFormLayout(whatsapp_group)

        self.whatsapp_message_input = QTextEdit()
        self.whatsapp_message_input.setMaximumHeight(100)
        self.whatsapp_message_input.setPlainText(
            "السلام عليكم {Name}،\n\nنرفق لكم خطاب إداري.\n\nشكراً لكم."
        )
        whatsapp_layout.addRow("رسالة واتساب الافتراضية:", self.whatsapp_message_input)

        self.auto_whatsapp_check = QCheckBox("فتح واتساب تلقائياً بعد إنشاء الخطاب")
        whatsapp_layout.addRow("فتح تلقائي:", self.auto_whatsapp_check)

        layout.addWidget(whatsapp_group)
        layout.addStretch()

        self.tab_widget.addTab(letters_widget, "الخطابات")

    def create_advanced_tab(self):
        """إنشاء تبويب الإعدادات المتقدمة"""
        advanced_widget = QWidget()
        layout = QVBoxLayout(advanced_widget)

        # إعدادات قاعدة البيانات
        db_group = QGroupBox("إعدادات قاعدة البيانات")
        db_layout = QFormLayout(db_group)

        self.backup_interval_spin = QSpinBox()
        self.backup_interval_spin.setRange(1, 30)
        self.backup_interval_spin.setValue(7)
        self.backup_interval_spin.setSuffix(" أيام")
        db_layout.addRow("فترة النسخ الاحتياطية:", self.backup_interval_spin)

        self.max_backups_spin = QSpinBox()
        self.max_backups_spin.setRange(1, 50)
        self.max_backups_spin.setValue(10)
        db_layout.addRow("عدد النسخ الاحتياطية:", self.max_backups_spin)

        layout.addWidget(db_group)

        # إعدادات الأداء
        performance_group = QGroupBox("إعدادات الأداء")
        performance_layout = QFormLayout(performance_group)

        self.cache_size_spin = QSpinBox()
        self.cache_size_spin.setRange(10, 1000)
        self.cache_size_spin.setValue(100)
        self.cache_size_spin.setSuffix(" MB")
        performance_layout.addRow("حجم الذاكرة المؤقتة:", self.cache_size_spin)

        self.lazy_loading_check = QCheckBox("تحميل البيانات عند الحاجة")
        self.lazy_loading_check.setChecked(True)
        performance_layout.addRow("التحميل الذكي:", self.lazy_loading_check)

        layout.addWidget(performance_group)

        # إعدادات السجلات
        logging_group = QGroupBox("إعدادات السجلات")
        logging_layout = QFormLayout(logging_group)

        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["خطأ", "تحذير", "معلومات", "تشخيص"])
        self.log_level_combo.setCurrentText("معلومات")
        logging_layout.addRow("مستوى السجل:", self.log_level_combo)

        self.log_retention_spin = QSpinBox()
        self.log_retention_spin.setRange(1, 365)
        self.log_retention_spin.setValue(30)
        self.log_retention_spin.setSuffix(" يوم")
        logging_layout.addRow("مدة حفظ السجلات:", self.log_retention_spin)

        layout.addWidget(logging_group)

        # أزرار الصيانة
        maintenance_group = QGroupBox("صيانة التطبيق")
        maintenance_layout = QVBoxLayout(maintenance_group)

        clear_cache_btn = QPushButton("مسح الذاكرة المؤقتة")
        clear_cache_btn.clicked.connect(self.clear_cache)
        maintenance_layout.addWidget(clear_cache_btn)

        reset_settings_btn = QPushButton("إعادة تعيين الإعدادات")
        reset_settings_btn.clicked.connect(self.reset_settings)
        reset_settings_btn.setStyleSheet("QPushButton { background-color: #dc3545; color: white; }")
        maintenance_layout.addWidget(reset_settings_btn)

        layout.addWidget(maintenance_group)
        layout.addStretch()

        self.tab_widget.addTab(advanced_widget, "متقدم")

    def create_control_buttons(self, parent_layout):
        """إنشاء أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-top: 1px solid #dee2e6;
                padding: 10px;
            }
        """)
        buttons_layout = QHBoxLayout(buttons_frame)

        self.apply_btn = QPushButton("تطبيق")
        self.apply_btn.setStyleSheet(self.get_button_style("#28a745"))

        self.ok_btn = QPushButton("موافق")
        self.ok_btn.setStyleSheet(self.get_button_style("#007bff"))

        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setStyleSheet(self.get_button_style("#6c757d"))

        buttons_layout.addStretch()
        buttons_layout.addWidget(self.apply_btn)
        buttons_layout.addWidget(self.ok_btn)
        buttons_layout.addWidget(self.cancel_btn)

        parent_layout.addWidget(buttons_frame)

    def get_button_style(self, color: str) -> str:
        """الحصول على نمط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.2)};
            }}
        """

    def darken_color(self, color: str, factor: float = 0.1) -> str:
        """تغميق اللون"""
        color_map = {
            "#28a745": "#218838",
            "#007bff": "#0056b3",
            "#6c757d": "#545b62",
            "#dc3545": "#c82333"
        }
        return color_map.get(color, color)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.apply_btn.clicked.connect(self.apply_settings)
        self.ok_btn.clicked.connect(self.accept_settings)
        self.cancel_btn.clicked.connect(self.reject)

        # ربط التغييرات بالمعاينة
        self.font_family_combo.currentTextChanged.connect(self.preview_font)
        self.font_size_spin.valueChanged.connect(self.preview_font)
        self.bold_check.toggled.connect(self.preview_font)

    def choose_folder(self, line_edit: QLineEdit):
        """اختيار مجلد"""
        folder = QFileDialog.getExistingDirectory(
            self, "اختيار مجلد", line_edit.text()
        )
        if folder:
            line_edit.setText(folder)

    def preview_font(self):
        """معاينة الخط"""
        try:
            font_family = self.font_family_combo.currentText()
            font_size = self.font_size_spin.value()
            is_bold = self.bold_check.isChecked()

            font = QFont(font_family, font_size)
            if is_bold:
                font.setBold(True)

            # تطبيق الخط على النافذة للمعاينة
            self.setFont(font)
        except Exception as e:
            print(f"خطأ في معاينة الخط: {e}")

    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    self.current_settings = json.load(f)
            else:
                self.current_settings = self.get_default_settings()

            self.apply_settings_to_ui()

        except Exception as e:
            QMessageBox.warning(
                self, "تحذير",
                f"خطأ في تحميل الإعدادات:\n{e}\n\nسيتم استخدام الإعدادات الافتراضية"
            )
            self.current_settings = self.get_default_settings()
            self.apply_settings_to_ui()

    def get_default_settings(self) -> Dict[str, Any]:
        """الحصول على الإعدادات الافتراضية"""
        return {
            # إعدادات عامة
            "auto_save": True,
            "auto_backup": True,
            "startup": False,
            "templates_folder": str(BASE_DIR / "templates"),
            "output_folder": str(BASE_DIR / "output"),
            "language": "العربية",
            "date_format": "DD/MM/YYYY",

            # إعدادات المظهر
            "font_family": "Tahoma",
            "font_size": 12,
            "font_bold": False,
            "theme": "فاتح",
            "accent_color": "أزرق",
            "window_width": 1200,
            "window_height": 800,
            "maximize_window": False,

            # إعدادات الخطابات
            "letter_prefix": "خطاب-",
            "auto_numbering": True,
            "auto_pdf": True,
            "default_template": "قالب افتراضي",
            "template_validation": True,
            "whatsapp_message": "السلام عليكم {Name}،\n\nنرفق لكم خطاب إداري.\n\nشكراً لكم.",
            "auto_whatsapp": False,

            # إعدادات متقدمة
            "backup_interval": 7,
            "max_backups": 10,
            "cache_size": 100,
            "lazy_loading": True,
            "log_level": "معلومات",
            "log_retention": 30
        }

    def apply_settings_to_ui(self):
        """تطبيق الإعدادات على واجهة المستخدم"""
        settings = self.current_settings

        # إعدادات عامة
        self.auto_save_check.setChecked(settings.get("auto_save", True))
        self.backup_check.setChecked(settings.get("auto_backup", True))
        self.startup_check.setChecked(settings.get("startup", False))
        self.templates_folder_input.setText(settings.get("templates_folder", ""))
        self.output_folder_input.setText(settings.get("output_folder", ""))

        language = settings.get("language", "العربية")
        language_index = self.language_combo.findText(language)
        if language_index >= 0:
            self.language_combo.setCurrentIndex(language_index)

        date_format = settings.get("date_format", "DD/MM/YYYY")
        date_index = self.date_format_combo.findText(date_format)
        if date_index >= 0:
            self.date_format_combo.setCurrentIndex(date_index)

        # إعدادات المظهر
        font_family = settings.get("font_family", "Tahoma")
        font_index = self.font_family_combo.findText(font_family)
        if font_index >= 0:
            self.font_family_combo.setCurrentIndex(font_index)
        else:
            self.font_family_combo.setCurrentText(font_family)

        self.font_size_spin.setValue(settings.get("font_size", 12))
        self.bold_check.setChecked(settings.get("font_bold", False))

        theme = settings.get("theme", "فاتح")
        theme_index = self.theme_combo.findText(theme)
        if theme_index >= 0:
            self.theme_combo.setCurrentIndex(theme_index)

        accent_color = settings.get("accent_color", "أزرق")
        accent_index = self.accent_color_combo.findText(accent_color)
        if accent_index >= 0:
            self.accent_color_combo.setCurrentIndex(accent_index)

        self.window_width_spin.setValue(settings.get("window_width", 1200))
        self.window_height_spin.setValue(settings.get("window_height", 800))
        self.maximize_check.setChecked(settings.get("maximize_window", False))

        # إعدادات الخطابات
        self.letter_prefix_input.setText(settings.get("letter_prefix", "خطاب-"))
        self.auto_number_check.setChecked(settings.get("auto_numbering", True))
        self.auto_pdf_check.setChecked(settings.get("auto_pdf", True))
        self.template_validation_check.setChecked(settings.get("template_validation", True))
        self.whatsapp_message_input.setPlainText(settings.get("whatsapp_message", ""))
        self.auto_whatsapp_check.setChecked(settings.get("auto_whatsapp", False))

        # إعدادات متقدمة
        self.backup_interval_spin.setValue(settings.get("backup_interval", 7))
        self.max_backups_spin.setValue(settings.get("max_backups", 10))
        self.cache_size_spin.setValue(settings.get("cache_size", 100))
        self.lazy_loading_check.setChecked(settings.get("lazy_loading", True))

        log_level = settings.get("log_level", "معلومات")
        log_index = self.log_level_combo.findText(log_level)
        if log_index >= 0:
            self.log_level_combo.setCurrentIndex(log_index)

        self.log_retention_spin.setValue(settings.get("log_retention", 30))

    def collect_settings_from_ui(self) -> Dict[str, Any]:
        """جمع الإعدادات من واجهة المستخدم"""
        return {
            # إعدادات عامة
            "auto_save": self.auto_save_check.isChecked(),
            "auto_backup": self.backup_check.isChecked(),
            "startup": self.startup_check.isChecked(),
            "templates_folder": self.templates_folder_input.text(),
            "output_folder": self.output_folder_input.text(),
            "language": self.language_combo.currentText(),
            "date_format": self.date_format_combo.currentText(),

            # إعدادات المظهر
            "font_family": self.font_family_combo.currentText(),
            "font_size": self.font_size_spin.value(),
            "font_bold": self.bold_check.isChecked(),
            "theme": self.theme_combo.currentText(),
            "accent_color": self.accent_color_combo.currentText(),
            "window_width": self.window_width_spin.value(),
            "window_height": self.window_height_spin.value(),
            "maximize_window": self.maximize_check.isChecked(),

            # إعدادات الخطابات
            "letter_prefix": self.letter_prefix_input.text(),
            "auto_numbering": self.auto_number_check.isChecked(),
            "auto_pdf": self.auto_pdf_check.isChecked(),
            "default_template": self.default_template_combo.currentText(),
            "template_validation": self.template_validation_check.isChecked(),
            "whatsapp_message": self.whatsapp_message_input.toPlainText(),
            "auto_whatsapp": self.auto_whatsapp_check.isChecked(),

            # إعدادات متقدمة
            "backup_interval": self.backup_interval_spin.value(),
            "max_backups": self.max_backups_spin.value(),
            "cache_size": self.cache_size_spin.value(),
            "lazy_loading": self.lazy_loading_check.isChecked(),
            "log_level": self.log_level_combo.currentText(),
            "log_retention": self.log_retention_spin.value()
        }

    def save_settings(self, settings: Dict[str, Any]):
        """حفظ الإعدادات"""
        try:
            # التأكد من وجود مجلد الإعدادات
            self.settings_file.parent.mkdir(parents=True, exist_ok=True)

            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

            self.current_settings = settings
            return True

        except Exception as e:
            QMessageBox.critical(
                self, "خطأ",
                f"خطأ في حفظ الإعدادات:\n{e}"
            )
            return False

    def apply_settings(self):
        """تطبيق الإعدادات"""
        new_settings = self.collect_settings_from_ui()

        if self.save_settings(new_settings):
            self.settings_changed.emit(new_settings)
            QMessageBox.information(
                self, "نجح",
                "تم تطبيق الإعدادات بنجاح"
            )

    def accept_settings(self):
        """قبول الإعدادات وإغلاق النافذة"""
        self.apply_settings()
        self.accept()

    def clear_cache(self):
        """مسح الذاكرة المؤقتة"""
        reply = QMessageBox.question(
            self, "تأكيد",
            "هل أنت متأكد من مسح الذاكرة المؤقتة؟",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # مسح ملفات الذاكرة المؤقتة
                cache_dir = BASE_DIR / "cache"
                if cache_dir.exists():
                    import shutil
                    shutil.rmtree(cache_dir)
                    cache_dir.mkdir()

                QMessageBox.information(
                    self, "نجح",
                    "تم مسح الذاكرة المؤقتة بنجاح"
                )
            except Exception as e:
                QMessageBox.critical(
                    self, "خطأ",
                    f"خطأ في مسح الذاكرة المؤقتة:\n{e}"
                )

    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(
            self, "تأكيد",
            "هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟\n"
            "سيتم فقدان جميع الإعدادات المخصصة.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.current_settings = self.get_default_settings()
            self.apply_settings_to_ui()
            QMessageBox.information(
                self, "نجح",
                "تم إعادة تعيين الإعدادات بنجاح"
            )
