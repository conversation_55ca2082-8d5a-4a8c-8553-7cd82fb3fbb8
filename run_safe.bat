@echo off
chcp 65001 >nul
title نظام إدارة خطابات المستأجرين - تشغيل آمن

echo.
echo ================================================================
echo    نظام إدارة خطابات المستأجرين
echo    تشغيل آمن مع معالجة أخطاء التواريخ
echo ================================================================
echo.

echo 🛡️ تشغيل التطبيق الآمن...
echo.

python run_safe.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في التشغيل الآمن
    echo.
    echo 💡 جرب الحلول البديلة:
    echo.
    echo 1. إصلاح خطأ التواريخ:
    echo    python fix_date_error.py
    echo.
    echo 2. التشغيل النظيف:
    echo    python run_clean.py
    echo.
    echo 3. إصلاح الأرشيف:
    echo    python fix_archive.py
    echo.
    echo 4. التشغيل المثالي:
    echo    python run_arabic_perfect.py
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق التطبيق بنجاح
    echo 🛡️ التطبيق عمل بشكل آمن ومستقر!
    echo 📅 أخطاء التواريخ تم حلها
)

echo.
pause
