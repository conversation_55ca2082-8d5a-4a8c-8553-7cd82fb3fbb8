@echo off
chcp 65001 >nul
title نظام إدارة خطابات المستأجرين - RTL Professional

echo.
echo ================================================================
echo    نظام إدارة خطابات المستأجرين - الإصدار النهائي
echo    دعم RTL كامل + واجهة احترافية
echo ================================================================
echo.

echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث من python.org
    echo.
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python %PYTHON_VERSION% متوفر

echo.
echo 🚀 تشغيل التطبيق النهائي مع RTL...
echo.

python run_final.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق
    echo.
    echo 💡 جرب الحلول التالية بالترتيب:
    echo.
    echo 1. تحديث التطبيق:
    echo    python update_app.py
    echo.
    echo 2. تثبيت المكتبات:
    echo    pip install -r requirements.txt
    echo.
    echo 3. التشغيل البديل:
    echo    python start_app.py
    echo.
    echo 4. التشغيل المحسن:
    echo    python run_v2.py
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق التطبيق بنجاح
    echo 🙏 شكراً لاستخدام نظام إدارة خطابات المستأجرين
)

echo.
pause
