# -*- coding: utf-8 -*-
"""دعم اللغة العربية المحسن"""

from PySide6.QtWidgets import QWidget, QApplication
from PySide6.QtCore import Qt, QLocale
from PySide6.QtGui import QFont

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT_AVAILABLE = True
except ImportError:
    ARABIC_SUPPORT_AVAILABLE = False

class ArabicSupport:
    @staticmethod
    def reshape_arabic_text(text: str) -> str:
        if not ARABIC_SUPPORT_AVAILABLE or not text:
            return text
        try:
            cleaned_text = text.strip()
            if not ArabicSupport._contains_arabic_chars(cleaned_text):
                return cleaned_text
            try:
                reshaped_text = arabic_reshaper.reshape(
                    cleaned_text, delete_harakat=False, support_zwj=True, support_zwnj=True)
            except TypeError:
                reshaped_text = arabic_reshaper.reshape(cleaned_text)
            return get_display(reshaped_text, base_dir='R')
        except Exception:
            return text

    @staticmethod
    def _contains_arabic_chars(text: str) -> bool:
        try:
            return any(0x0600 <= ord(char) <= 0x06FF for char in text)
        except:
            return False

    @staticmethod
    def setup_application_rtl(app: QApplication):
        try:
            locale = QLocale(QLocale.Arabic, QLocale.SaudiArabia)
            QLocale.setDefault(locale)
            app.setLayoutDirection(Qt.RightToLeft)
        except Exception:
            pass

    @staticmethod
    def setup_arabic_font(widget: QWidget, size: int = 12):
        try:
            font = QFont()
            for font_family in ["Tahoma", "Arial", "Segoe UI"]:
                font.setFamily(font_family)
                if font.exactMatch():
                    break
            font.setPointSize(size)
            widget.setFont(font)
        except Exception:
            pass

    @staticmethod
    def setup_rtl_layout(widget: QWidget):
        try:
            widget.setLayoutDirection(Qt.RightToLeft)
        except Exception:
            pass
