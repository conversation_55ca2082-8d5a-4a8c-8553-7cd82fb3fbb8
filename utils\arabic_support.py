# -*- coding: utf-8 -*-
"""
دعم اللغة العربية
Arabic Language Support
"""

import arabic_reshaper
from bidi.algorithm import get_display
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont
from PySide6.QtWidgets import QWidget


class ArabicSupport:
    """فئة دعم اللغة العربية"""

    @staticmethod
    def reshape_arabic_text(text: str) -> str:
        """إعادة تشكيل النص العربي للعرض الصحيح"""
        if not text:
            return ""

        try:
            # إعادة تشكيل النص العربي
            reshaped_text = arabic_reshaper.reshape(text)
            # تطبيق خوارزمية الاتجاه الثنائي
            display_text = get_display(reshaped_text)
            return display_text
        except Exception as e:
            # في حالة الخطأ، إرجاع النص الأصلي
            return text

    @staticmethod
    def setup_arabic_font(widget: QWidget, font_size: int = 12):
        """إعداد خط عربي للعنصر"""
        try:
            # قائمة الخطوط العربية المفضلة
            arabic_fonts = [
                "Tahoma",
                "Arial Unicode MS",
                "Segoe UI",
                "Microsoft Sans Serif",
                "Times New Roman",
                "Calibri",
                "Verdana"
            ]

            font = QFont()
            font.setPointSize(font_size)
            font.setStyleHint(QFont.SansSerif)

            # تجربة الخطوط بالترتيب
            for font_name in arabic_fonts:
                font.setFamily(font_name)
                if font.exactMatch():
                    break

            # إعدادات إضافية للخط العربي
            font.setKerning(True)
            font.setStyleStrategy(QFont.PreferAntialias)

            widget.setFont(font)

            # إعداد خصائص النص للعربية
            if hasattr(widget, 'setTextDirection'):
                widget.setTextDirection(Qt.RightToLeft)

        except Exception as e:
            print(f"خطأ في إعداد الخط العربي: {e}")

    @staticmethod
    def setup_advanced_rtl(widget: QWidget):
        """إعداد متقدم للاتجاه من اليمين لليسار"""
        try:
            widget.setLayoutDirection(Qt.RightToLeft)

            # إعداد خصائص إضافية للنصوص العربية
            if hasattr(widget, 'setAlignment'):
                widget.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

            # إعداد CSS للنصوص العربية
            widget.setStyleSheet(widget.styleSheet() + """
                QLineEdit, QTextEdit, QPlainTextEdit {
                    text-align: right;
                    direction: rtl;
                }
                QLabel {
                    text-align: right;
                }
            """)

        except Exception as e:
            print(f"خطأ في إعداد RTL المتقدم: {e}")

    @staticmethod
    def fix_arabic_text_rendering(text: str) -> str:
        """إصلاح عرض النصوص العربية المعقدة"""
        if not text:
            return ""

        try:
            # إعادة تشكيل النص العربي مع إعدادات محسنة
            reshaped_text = arabic_reshaper.reshape(text)

            # تطبيق خوارزمية الاتجاه الثنائي
            display_text = get_display(reshaped_text, base_dir='R')

            return display_text

        except Exception as e:
            print(f"خطأ في إصلاح عرض النص العربي: {e}")
            return text

    @staticmethod
    def setup_rtl_layout(widget: QWidget):
        """إعداد التخطيط من اليمين لليسار"""
        try:
            widget.setLayoutDirection(Qt.RightToLeft)
        except Exception as e:
            print(f"خطأ في إعداد التخطيط RTL: {e}")

    @staticmethod
    def format_arabic_date(date_obj) -> str:
        """تنسيق التاريخ بالعربية"""
        try:
            if not date_obj:
                return ""

            # أسماء الأشهر بالعربية
            arabic_months = [
                "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
            ]

            day = date_obj.day
            month = arabic_months[date_obj.month - 1]
            year = date_obj.year

            return f"{day} {month} {year}"

        except Exception as e:
            print(f"خطأ في تنسيق التاريخ: {e}")
            return str(date_obj) if date_obj else ""

    @staticmethod
    def convert_numbers_to_arabic(text: str) -> str:
        """تحويل الأرقام الإنجليزية إلى عربية"""
        if not text:
            return ""

        try:
            # خريطة تحويل الأرقام
            english_to_arabic = {
                '0': '٠', '1': '١', '2': '٢', '3': '٣', '4': '٤',
                '5': '٥', '6': '٦', '7': '٧', '8': '٨', '9': '٩'
            }

            result = text
            for eng, ara in english_to_arabic.items():
                result = result.replace(eng, ara)

            return result

        except Exception as e:
            print(f"خطأ في تحويل الأرقام: {e}")
            return text

    @staticmethod
    def convert_numbers_to_english(text: str) -> str:
        """تحويل الأرقام العربية إلى إنجليزية"""
        if not text:
            return ""

        try:
            # خريطة تحويل الأرقام
            arabic_to_english = {
                '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
                '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
            }

            result = text
            for ara, eng in arabic_to_english.items():
                result = result.replace(ara, eng)

            return result

        except Exception as e:
            print(f"خطأ في تحويل الأرقام: {e}")
            return text
