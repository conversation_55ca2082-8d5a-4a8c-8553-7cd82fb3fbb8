# -*- coding: utf-8 -*-
"""
دعم اللغة العربية
Arabic Language Support
"""

import arabic_reshaper
from bidi.algorithm import get_display
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont
from PySide6.QtWidgets import QWidget


class ArabicSupport:
    """فئة دعم اللغة العربية"""

    @staticmethod
    def reshape_arabic_text(text: str) -> str:
        """تشكيل النص العربي للعرض الصحيح مع إصلاح مشاكل التشكيل"""
        if not text:
            return ""

        try:
            # تنظيف النص أولاً
            cleaned_text = text.strip()

            # فحص إذا كان النص يحتوي على أحرف عربية
            if not ArabicSupport._contains_arabic_chars(cleaned_text):
                return cleaned_text

            # تشكيل النص العربي باستخدام arabic-reshaper
            try:
                # محاولة استخدام الإعدادات المحسنة
                reshaped_text = arabic_reshaper.reshape(
                    cleaned_text,
                    delete_harakat=False,
                    support_zwj=True,
                    support_zwnj=True
                )
            except TypeError:
                # إذا فشلت، استخدم الطريقة الأساسية
                reshaped_text = arabic_reshaper.reshape(cleaned_text)

            # تطبيق اتجاه RTL باستخدام python-bidi
            display_text = get_display(reshaped_text, base_dir='R')

            return display_text

        except Exception as e:
            print(f"خطأ في تشكيل النص العربي: {e}")
            # في حالة الخطأ، إرجاع النص الأصلي
            return text

    @staticmethod
    def _contains_arabic_chars(text: str) -> bool:
        """فحص إذا كان النص يحتوي على أحرف عربية"""
        try:
            # نطاق الأحرف العربية في Unicode
            arabic_ranges = [
                (0x0600, 0x06FF),  # Arabic
                (0x0750, 0x077F),  # Arabic Supplement
                (0x08A0, 0x08FF),  # Arabic Extended-A
                (0xFB50, 0xFDFF),  # Arabic Presentation Forms-A
                (0xFE70, 0xFEFF),  # Arabic Presentation Forms-B
            ]

            for char in text:
                char_code = ord(char)
                for start, end in arabic_ranges:
                    if start <= char_code <= end:
                        return True
            return False
        except:
            return False

    @staticmethod
    def setup_application_rtl(app):
        """إعداد التطبيق الكامل للـ RTL والعربية"""
        try:
            from PySide6.QtCore import Qt
            from PySide6.QtGui import QFont

            # تعيين اتجاه التطبيق للـ RTL بقوة
            app.setLayoutDirection(Qt.RightToLeft)

            # إعداد الخط الافتراضي للعربية
            font = QFont("Tahoma", 11)
            font.setStyleHint(QFont.SansSerif)
            font.setStyleStrategy(QFont.PreferAntialias)
            font.setKerning(True)
            app.setFont(font)

            # إعداد متغيرات البيئة للـ RTL
            import os
            os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
            os.environ['QT_ENABLE_HIGHDPI_SCALING'] = '1'

            # إعداد stylesheet شامل للـ RTL
            rtl_stylesheet = """
                QApplication {
                    font-family: 'Tahoma', 'Arial Unicode MS', 'Segoe UI';
                    direction: rtl;
                }

                /* حقول النص */
                QLineEdit, QTextEdit, QPlainTextEdit {
                    text-align: right;
                    direction: rtl;
                    padding: 8px;
                    border: 2px solid #e0e0e0;
                    border-radius: 6px;
                    background-color: white;
                    font-size: 11px;
                }

                QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
                    border-color: #007bff;
                    background-color: #f8f9ff;
                }

                /* التسميات */
                QLabel {
                    text-align: right;
                    direction: rtl;
                    color: #333;
                }

                /* القوائم المنسدلة */
                QComboBox {
                    text-align: right;
                    direction: rtl;
                    padding: 8px 12px;
                    border: 2px solid #e0e0e0;
                    border-radius: 6px;
                    background-color: white;
                    min-height: 20px;
                }

                QComboBox:focus {
                    border-color: #007bff;
                }

                QComboBox::drop-down {
                    border: none;
                    width: 20px;
                }

                QComboBox::down-arrow {
                    image: none;
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                    border-top: 5px solid #666;
                }

                /* الجداول */
                QTableWidget {
                    direction: rtl;
                    gridline-color: #e0e0e0;
                    background-color: white;
                    alternate-background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 6px;
                }

                QTableWidget::item {
                    text-align: right;
                    padding: 8px;
                    border-bottom: 1px solid #e0e0e0;
                }

                QTableWidget::item:selected {
                    background-color: #007bff;
                    color: white;
                }

                QHeaderView::section {
                    text-align: right;
                    padding: 10px 8px;
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 0px;
                    font-weight: bold;
                    color: #495057;
                }

                /* القوائم */
                QMenuBar {
                    direction: rtl;
                    background-color: #f8f9fa;
                    border-bottom: 1px solid #dee2e6;
                    padding: 4px;
                }

                QMenuBar::item {
                    padding: 8px 12px;
                    margin: 2px;
                    border-radius: 4px;
                }

                QMenuBar::item:selected {
                    background-color: #007bff;
                    color: white;
                }

                QMenu {
                    direction: rtl;
                    background-color: white;
                    border: 1px solid #dee2e6;
                    border-radius: 6px;
                    padding: 4px;
                }

                QMenu::item {
                    padding: 8px 20px;
                    margin: 2px;
                    border-radius: 4px;
                }

                QMenu::item:selected {
                    background-color: #007bff;
                    color: white;
                }

                /* التبويبات */
                QTabWidget::pane {
                    border: 1px solid #dee2e6;
                    border-radius: 6px;
                    background-color: white;
                    margin-top: 2px;
                }

                QTabBar {
                    direction: rtl;
                }

                QTabBar::tab {
                    text-align: right;
                    padding: 10px 20px;
                    margin-left: 2px;
                    border-top-left-radius: 6px;
                    border-top-right-radius: 6px;
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-bottom: none;
                }

                QTabBar::tab:selected {
                    background-color: white;
                    border-bottom: 2px solid white;
                }

                QTabBar::tab:hover {
                    background-color: #e9ecef;
                }

                /* الأزرار */
                QPushButton {
                    text-align: center;
                    padding: 10px 20px;
                    border: none;
                    border-radius: 6px;
                    font-weight: bold;
                    min-width: 80px;
                    min-height: 35px;
                }

                QPushButton:hover {
                    transform: translateY(-1px);
                }

                QPushButton:pressed {
                    transform: translateY(1px);
                }

                /* مربعات الاختيار */
                QCheckBox {
                    direction: rtl;
                    spacing: 8px;
                }

                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                    border: 2px solid #dee2e6;
                    border-radius: 3px;
                    background-color: white;
                }

                QCheckBox::indicator:checked {
                    background-color: #007bff;
                    border-color: #007bff;
                }

                /* أشرطة التمرير */
                QScrollBar:vertical {
                    background-color: #f8f9fa;
                    width: 12px;
                    border-radius: 6px;
                }

                QScrollBar::handle:vertical {
                    background-color: #dee2e6;
                    border-radius: 6px;
                    min-height: 20px;
                }

                QScrollBar::handle:vertical:hover {
                    background-color: #adb5bd;
                }

                /* مجموعات العناصر */
                QGroupBox {
                    font-weight: bold;
                    border: 2px solid #dee2e6;
                    border-radius: 8px;
                    margin-top: 10px;
                    padding-top: 10px;
                }

                QGroupBox::title {
                    subcontrol-origin: margin;
                    subcontrol-position: top right;
                    padding: 0 10px;
                    background-color: white;
                    color: #495057;
                }
            """

            # دمج الأنماط الاحترافية
            try:
                from ui.professional_styles import ProfessionalStyles
                professional_style = ProfessionalStyles.get_complete_professional_style()
                combined_style = rtl_stylesheet + "\n" + professional_style
                app.setStyleSheet(combined_style)
            except ImportError:
                app.setStyleSheet(rtl_stylesheet)

            return True

        except Exception as e:
            print(f"خطأ في إعداد RTL للتطبيق: {e}")
            return False

    @staticmethod
    def setup_arabic_font(widget: QWidget, font_size: int = 12):
        """إعداد خط عربي للعنصر"""
        try:
            # قائمة الخطوط العربية المفضلة
            arabic_fonts = [
                "Tahoma",
                "Arial Unicode MS",
                "Segoe UI",
                "Microsoft Sans Serif",
                "Times New Roman",
                "Calibri",
                "Verdana"
            ]

            font = QFont()
            font.setPointSize(font_size)
            font.setStyleHint(QFont.SansSerif)

            # تجربة الخطوط بالترتيب
            for font_name in arabic_fonts:
                font.setFamily(font_name)
                if font.exactMatch():
                    break

            # إعدادات إضافية للخط العربي
            font.setKerning(True)
            font.setStyleStrategy(QFont.PreferAntialias)

            widget.setFont(font)

            # إعداد خصائص النص للعربية
            if hasattr(widget, 'setTextDirection'):
                widget.setTextDirection(Qt.RightToLeft)

        except Exception as e:
            print(f"خطأ في إعداد الخط العربي: {e}")

    @staticmethod
    def setup_advanced_rtl(widget: QWidget):
        """إعداد متقدم للاتجاه من اليمين لليسار"""
        try:
            widget.setLayoutDirection(Qt.RightToLeft)

            # إعداد خصائص إضافية للنصوص العربية
            if hasattr(widget, 'setAlignment'):
                widget.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

            # إعداد CSS للنصوص العربية
            widget.setStyleSheet(widget.styleSheet() + """
                QLineEdit, QTextEdit, QPlainTextEdit {
                    text-align: right;
                    direction: rtl;
                }
                QLabel {
                    text-align: right;
                }
            """)

        except Exception as e:
            print(f"خطأ في إعداد RTL المتقدم: {e}")

    @staticmethod
    def fix_arabic_text_rendering(text: str) -> str:
        """إصلاح عرض النصوص العربية المعقدة"""
        if not text:
            return ""

        try:
            # إعادة تشكيل النص العربي مع إعدادات محسنة
            reshaped_text = arabic_reshaper.reshape(text)

            # تطبيق خوارزمية الاتجاه الثنائي
            display_text = get_display(reshaped_text, base_dir='R')

            return display_text

        except Exception as e:
            print(f"خطأ في إصلاح عرض النص العربي: {e}")
            return text

    @staticmethod
    def setup_rtl_layout(widget: QWidget):
        """إعداد التخطيط من اليمين لليسار"""
        try:
            widget.setLayoutDirection(Qt.RightToLeft)
        except Exception as e:
            print(f"خطأ في إعداد التخطيط RTL: {e}")

    @staticmethod
    def format_arabic_date(date_obj) -> str:
        """تنسيق التاريخ بالعربية"""
        try:
            if not date_obj:
                return ""

            # أسماء الأشهر بالعربية
            arabic_months = [
                "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
            ]

            day = date_obj.day
            month = arabic_months[date_obj.month - 1]
            year = date_obj.year

            return f"{day} {month} {year}"

        except Exception as e:
            print(f"خطأ في تنسيق التاريخ: {e}")
            return str(date_obj) if date_obj else ""

    @staticmethod
    def convert_numbers_to_arabic(text: str) -> str:
        """تحويل الأرقام الإنجليزية إلى عربية"""
        if not text:
            return ""

        try:
            # خريطة تحويل الأرقام
            english_to_arabic = {
                '0': '٠', '1': '١', '2': '٢', '3': '٣', '4': '٤',
                '5': '٥', '6': '٦', '7': '٧', '8': '٨', '9': '٩'
            }

            result = text
            for eng, ara in english_to_arabic.items():
                result = result.replace(eng, ara)

            return result

        except Exception as e:
            print(f"خطأ في تحويل الأرقام: {e}")
            return text

    @staticmethod
    def convert_numbers_to_english(text: str) -> str:
        """تحويل الأرقام العربية إلى إنجليزية"""
        if not text:
            return ""

        try:
            # خريطة تحويل الأرقام
            arabic_to_english = {
                '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
                '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
            }

            result = text
            for ara, eng in arabic_to_english.items():
                result = result.replace(ara, eng)

            return result

        except Exception as e:
            print(f"خطأ في تحويل الأرقام: {e}")
            return text
