# -*- coding: utf-8 -*-
"""
إعدادات التطبيق
Application Settings
"""

import os
from pathlib import Path

# مسارات المشروع - Project Paths
BASE_DIR = Path(__file__).parent.parent
DATA_DIR = BASE_DIR / "data"
TEMPLATES_DIR = BASE_DIR / "templates"
OUTPUT_DIR = BASE_DIR / "output"

# إعدادات قاعدة البيانات - Database Settings
DATABASE_PATH = DATA_DIR / "tenants.db"

# إعدادات التطبيق - Application Settings
APP_NAME = "نظام إدارة خطابات المستأجرين"
APP_VERSION = "1.0.0"
APP_AUTHOR = "Desktop as3am 7tab"

# إعدادات النافذة الرئيسية - Main Window Settings
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
WINDOW_MIN_WIDTH = 800
WINDOW_MIN_HEIGHT = 600

# إعدادات اللغة العربية - Arabic Language Settings
FONT_FAMILY = "Tahoma"
FONT_SIZE = 12
RTL_SUPPORT = True

# إعدادات الخطابات - Letters Settings
DEFAULT_LETTER_TEMPLATE = "قالب افتراضي.docx"
LETTER_NUMBER_PREFIX = "خطاب-"
DATE_FORMAT = "%Y-%m-%d"
ARABIC_DATE_FORMAT = "%d/%m/%Y"

# إعدادات الملفات - File Settings
SUPPORTED_TEMPLATE_FORMATS = [".docx"]
SUPPORTED_OUTPUT_FORMATS = [".docx", ".pdf"]

# إعدادات واتساب - WhatsApp Settings
WHATSAPP_WEB_URL = "https://web.whatsapp.com"

# إنشاء المجلدات المطلوبة - Create Required Directories
def create_directories():
    """إنشاء المجلدات المطلوبة للتطبيق"""
    directories = [DATA_DIR, TEMPLATES_DIR, OUTPUT_DIR]
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

# متغيرات القوالب المدعومة - Supported Template Variables
TEMPLATE_VARIABLES = {
    "{Name}": "اسم المستأجر",
    "{Unit}": "رقم الوحدة", 
    "{Building}": "رقم العمارة",
    "{LetterNo}": "رقم الخطاب",
    "{Date}": "تاريخ الخطاب",
    "{Content}": "محتوى الخطاب"
}

# أنواع القوالب - Template Types
TEMPLATE_TYPES = [
    "إنذار",
    "إشعار", 
    "تنبيه",
    "مراسلة عامة",
    "أخرى"
]
