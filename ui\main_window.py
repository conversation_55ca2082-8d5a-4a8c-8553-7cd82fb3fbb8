# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للتطبيق
Main Application Window
"""

import sys
import logging
from datetime import datetime
from typing import List

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
    QTableWidget, QTableWidgetItem, QPushButton, QLineEdit, QLabel,
    QMessageBox, QHeaderView, QAbstractItemView, QSplitter,
    QGroupBox, QTextEdit, QComboBox, QDateEdit, QSpacerItem, QSizePolicy
)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QIcon, QFont, QAction

from config.settings import (
    APP_NAME, WINDOW_WIDTH, WINDOW_HEIGHT, WINDOW_MIN_WIDTH, WINDOW_MIN_HEIGHT
)
from database.database_manager import DatabaseManager
from database.models import Tenant
from utils.arabic_support import ArabicSupport
from ui.tenant_dialog import TenantDialog
from ui.letter_dialog import LetterDialog
from ui.template_dialog import TemplateDialog
from ui.settings_dialog import SettingsDialog


class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""

    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_tenants: List[Tenant] = []

        self.init_ui()
        self.setup_connections()
        self.load_tenants()

        # إعداد دعم اللغة العربية والـ RTL
        ArabicSupport.setup_arabic_font(self)
        ArabicSupport.setup_rtl_layout(self)
        ArabicSupport.setup_advanced_rtl(self)

        # تطبيق التحسينات الاحترافية
        try:
            from utils.ui_enhancer import UIEnhancer
            UIEnhancer.enhance_main_window(self)
        except ImportError:
            pass

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle(APP_NAME)
        self.setGeometry(100, 100, WINDOW_WIDTH, WINDOW_HEIGHT)
        self.setMinimumSize(WINDOW_MIN_WIDTH, WINDOW_MIN_HEIGHT)

        # إنشاء القائمة الرئيسية
        self.create_menu_bar()

        # إنشاء شريط الأدوات
        self.create_toolbar()

        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)

        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # تبويب المستأجرين
        self.create_tenants_tab()

        # تبويب الخطابات
        self.create_letters_tab()

        # تبويب الأرشيف
        self.create_archive_tab()

        # شريط الحالة
        self.statusBar().showMessage("مرحباً بك في نظام إدارة خطابات المستأجرين")

    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()

        # قائمة ملف
        file_menu = menubar.addMenu("ملف")

        # إضافة مستأجر جديد
        add_tenant_action = QAction("إضافة مستأجر جديد", self)
        add_tenant_action.setShortcut("Ctrl+N")
        add_tenant_action.triggered.connect(self.add_tenant)
        file_menu.addAction(add_tenant_action)

        file_menu.addSeparator()

        # خروج
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # قائمة الخطابات
        letters_menu = menubar.addMenu("الخطابات")

        # إنشاء خطاب جديد
        new_letter_action = QAction("إنشاء خطاب جديد", self)
        new_letter_action.setShortcut("Ctrl+L")
        new_letter_action.triggered.connect(self.create_new_letter)
        letters_menu.addAction(new_letter_action)

        # قائمة الأدوات
        tools_menu = menubar.addMenu("أدوات")

        # إدارة القوالب
        templates_action = QAction("إدارة القوالب", self)
        templates_action.setShortcut("Ctrl+T")
        templates_action.triggered.connect(self.manage_templates)
        tools_menu.addAction(templates_action)

        # الإعدادات
        settings_action = QAction("الإعدادات", self)
        settings_action.setShortcut("Ctrl+,")
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")

        # حول البرنامج
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")

        # إضافة مستأجر
        add_tenant_btn = QPushButton("إضافة مستأجر")
        add_tenant_btn.clicked.connect(self.add_tenant)
        toolbar.addWidget(add_tenant_btn)

        toolbar.addSeparator()

        # إنشاء خطاب
        new_letter_btn = QPushButton("إنشاء خطاب")
        new_letter_btn.clicked.connect(self.create_new_letter)
        toolbar.addWidget(new_letter_btn)

        toolbar.addSeparator()

        # تحديث البيانات
        refresh_btn = QPushButton("تحديث")
        refresh_btn.clicked.connect(self.refresh_data)
        toolbar.addWidget(refresh_btn)

    def create_tenants_tab(self):
        """إنشاء تبويب المستأجرين"""
        tenants_widget = QWidget()
        layout = QVBoxLayout(tenants_widget)

        # شريط البحث والأدوات
        search_layout = QHBoxLayout()

        # البحث
        search_label = QLabel("البحث:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث بالاسم أو رقم الوحدة أو العمارة...")
        self.search_input.textChanged.connect(self.search_tenants)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)

        # أزرار الإدارة
        self.add_tenant_btn = QPushButton("إضافة مستأجر")
        self.edit_tenant_btn = QPushButton("تعديل")
        self.delete_tenant_btn = QPushButton("حذف")

        self.add_tenant_btn.clicked.connect(self.add_tenant)
        self.edit_tenant_btn.clicked.connect(self.edit_tenant)
        self.delete_tenant_btn.clicked.connect(self.delete_tenant)

        search_layout.addWidget(self.add_tenant_btn)
        search_layout.addWidget(self.edit_tenant_btn)
        search_layout.addWidget(self.delete_tenant_btn)

        layout.addLayout(search_layout)

        # جدول المستأجرين
        self.tenants_table = QTableWidget()
        self.tenants_table.setColumnCount(6)
        self.tenants_table.setHorizontalHeaderLabels([
            "الاسم", "رقم الوحدة", "رقم العمارة", "رقم الهاتف", "البريد الإلكتروني", "ملاحظات"
        ])

        # إعداد الجدول
        header = self.tenants_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.tenants_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.tenants_table.setAlternatingRowColors(True)

        # إعداد دعم اللغة العربية والتحسينات للجدول
        ArabicSupport.setup_arabic_font(self.tenants_table)
        ArabicSupport.setup_rtl_layout(self.tenants_table)

        # تطبيق التحسينات الاحترافية
        try:
            from utils.ui_enhancer import UIEnhancer
            UIEnhancer._enhance_tables(self.tenants_table)
        except ImportError:
            pass

        layout.addWidget(self.tenants_table)

        # إضافة التبويب
        self.tab_widget.addTab(tenants_widget, "المستأجرين")

    def create_letters_tab(self):
        """إنشاء تبويب الخطابات"""
        letters_widget = QWidget()
        layout = QVBoxLayout(letters_widget)

        # معلومات سريعة
        info_group = QGroupBox("معلومات سريعة")
        info_layout = QHBoxLayout(info_group)

        self.total_tenants_label = QLabel("إجمالي المستأجرين: 0")
        self.total_letters_label = QLabel("إجمالي الخطابات: 0")

        info_layout.addWidget(self.total_tenants_label)
        info_layout.addWidget(self.total_letters_label)
        info_layout.addStretch()

        layout.addWidget(info_group)

        # أزرار سريعة
        quick_actions_group = QGroupBox("إجراءات سريعة")
        quick_layout = QHBoxLayout(quick_actions_group)

        self.quick_letter_btn = QPushButton("إنشاء خطاب سريع")
        self.templates_btn = QPushButton("إدارة القوالب")
        self.export_btn = QPushButton("تصدير التقارير")

        self.quick_letter_btn.clicked.connect(self.create_new_letter)
        self.templates_btn.clicked.connect(self.manage_templates)
        self.export_btn.clicked.connect(self.export_reports)

        quick_layout.addWidget(self.quick_letter_btn)
        quick_layout.addWidget(self.templates_btn)
        quick_layout.addWidget(self.export_btn)
        quick_layout.addStretch()

        layout.addWidget(quick_actions_group)

        # مساحة فارغة
        layout.addStretch()

        # إضافة التبويب
        self.tab_widget.addTab(letters_widget, "الخطابات")

    def create_archive_tab(self):
        """إنشاء تبويب الأرشيف"""
        archive_widget = QWidget()
        layout = QVBoxLayout(archive_widget)

        # فلاتر البحث
        filter_layout = QHBoxLayout()

        filter_layout.addWidget(QLabel("من تاريخ:"))
        self.from_date = QDateEdit()
        self.from_date.setDate(QDate.currentDate().addDays(-30))
        filter_layout.addWidget(self.from_date)

        filter_layout.addWidget(QLabel("إلى تاريخ:"))
        self.to_date = QDateEdit()
        self.to_date.setDate(QDate.currentDate())
        filter_layout.addWidget(self.to_date)

        self.filter_btn = QPushButton("تطبيق الفلتر")
        filter_layout.addWidget(self.filter_btn)

        filter_layout.addStretch()

        layout.addLayout(filter_layout)

        # جدول الأرشيف
        self.archive_table = QTableWidget()
        self.archive_table.setColumnCount(5)
        self.archive_table.setHorizontalHeaderLabels([
            "رقم الخطاب", "اسم المستأجر", "نوع القالب", "تاريخ الإنشاء", "الحالة"
        ])

        # إعداد الجدول
        header = self.archive_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.archive_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.archive_table.setAlternatingRowColors(True)

        # إعداد دعم اللغة العربية
        ArabicSupport.setup_arabic_font(self.archive_table)
        ArabicSupport.setup_rtl_layout(self.archive_table)

        layout.addWidget(self.archive_table)

        # إضافة التبويب
        self.tab_widget.addTab(archive_widget, "الأرشيف")

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # تحديد صف في جدول المستأجرين
        self.tenants_table.selectionModel().selectionChanged.connect(
            self.on_tenant_selection_changed
        )

        # النقر المزدوج لتعديل المستأجر
        self.tenants_table.doubleClicked.connect(self.edit_tenant)

    def load_tenants(self):
        """تحميل بيانات المستأجرين"""
        try:
            self.current_tenants = self.db_manager.get_all_tenants()
            self.update_tenants_table()
            self.update_statistics()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات المستأجرين:\n{e}")

    def update_tenants_table(self):
        """تحديث جدول المستأجرين"""
        self.tenants_table.setRowCount(len(self.current_tenants))

        for row, tenant in enumerate(self.current_tenants):
            # إعداد البيانات مع دعم اللغة العربية
            name_item = QTableWidgetItem(ArabicSupport.reshape_arabic_text(tenant.name))
            unit_item = QTableWidgetItem(tenant.unit_number)
            building_item = QTableWidgetItem(tenant.building_number)
            phone_item = QTableWidgetItem(tenant.phone_number)
            email_item = QTableWidgetItem(tenant.email)
            notes_item = QTableWidgetItem(ArabicSupport.reshape_arabic_text(tenant.notes))

            # تخزين معرف المستأجر في العنصر الأول
            name_item.setData(Qt.UserRole, tenant.id)

            self.tenants_table.setItem(row, 0, name_item)
            self.tenants_table.setItem(row, 1, unit_item)
            self.tenants_table.setItem(row, 2, building_item)
            self.tenants_table.setItem(row, 3, phone_item)
            self.tenants_table.setItem(row, 4, email_item)
            self.tenants_table.setItem(row, 5, notes_item)

    def update_statistics(self):
        """تحديث الإحصائيات"""
        total_tenants = len(self.current_tenants)
        self.total_tenants_label.setText(f"إجمالي المستأجرين: {total_tenants}")

        # TODO: إضافة إحصائية الخطابات عند تنفيذ الوظيفة
        self.total_letters_label.setText("إجمالي الخطابات: 0")

    def search_tenants(self):
        """البحث في المستأجرين"""
        search_term = self.search_input.text().strip()

        if not search_term:
            self.current_tenants = self.db_manager.get_all_tenants()
        else:
            self.current_tenants = self.db_manager.search_tenants(search_term)

        self.update_tenants_table()

    def on_tenant_selection_changed(self):
        """عند تغيير تحديد المستأجر"""
        selected_rows = self.tenants_table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0

        self.edit_tenant_btn.setEnabled(has_selection)
        self.delete_tenant_btn.setEnabled(has_selection)

    def get_selected_tenant(self) -> Tenant:
        """الحصول على المستأجر المحدد"""
        selected_rows = self.tenants_table.selectionModel().selectedRows()
        if not selected_rows:
            return None

        row = selected_rows[0].row()
        tenant_id = self.tenants_table.item(row, 0).data(Qt.UserRole)

        # البحث عن المستأجر في القائمة الحالية
        for tenant in self.current_tenants:
            if tenant.id == tenant_id:
                return tenant

        return None

    def add_tenant(self):
        """إضافة مستأجر جديد"""
        dialog = TenantDialog(self)
        if dialog.exec() == TenantDialog.Accepted:
            self.load_tenants()

    def edit_tenant(self):
        """تعديل مستأجر"""
        tenant = self.get_selected_tenant()
        if not tenant:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مستأجر للتعديل")
            return

        dialog = TenantDialog(self, tenant)
        if dialog.exec() == TenantDialog.Accepted:
            self.load_tenants()

    def delete_tenant(self):
        """حذف مستأجر"""
        tenant = self.get_selected_tenant()
        if not tenant:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مستأجر للحذف")
            return

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف المستأجر '{tenant.name}'؟\n"
            "سيتم حذف جميع الخطابات المرتبطة به أيضاً.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                if self.db_manager.delete_tenant(tenant.id):
                    QMessageBox.information(self, "نجح", "تم حذف المستأجر بنجاح")
                    self.load_tenants()
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في حذف المستأجر")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف المستأجر:\n{e}")

    def create_new_letter(self):
        """إنشاء خطاب جديد"""
        # التأكد من وجود مستأجرين
        if not self.current_tenants:
            QMessageBox.warning(
                self, "تحذير",
                "لا يوجد مستأجرين مسجلين.\nيرجى إضافة مستأجر أولاً."
            )
            return

        dialog = LetterDialog(self)
        if dialog.exec() == LetterDialog.Accepted:
            # تحديث البيانات بعد إنشاء الخطاب
            self.refresh_data()

    def manage_templates(self):
        """إدارة القوالب"""
        dialog = TemplateDialog(self)
        dialog.template_updated.connect(self.refresh_data)
        dialog.exec()

    def export_reports(self):
        """تصدير التقارير"""
        # TODO: تنفيذ تصدير التقارير
        QMessageBox.information(self, "قريباً", "ستتوفر هذه الميزة قريباً")

    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.load_tenants()
        self.statusBar().showMessage("تم تحديث البيانات", 2000)

    def show_settings(self):
        """عرض نافذة الإعدادات"""
        dialog = SettingsDialog(self)
        dialog.settings_changed.connect(self.apply_new_settings)
        dialog.exec()

    def apply_new_settings(self, settings: dict):
        """تطبيق الإعدادات الجديدة"""
        try:
            # تطبيق إعدادات الخط
            font_family = settings.get("font_family", "Tahoma")
            font_size = settings.get("font_size", 12)
            font_bold = settings.get("font_bold", False)

            from PySide6.QtGui import QFont
            font = QFont(font_family, font_size)
            if font_bold:
                font.setBold(True)

            self.setFont(font)

            # تطبيق إعدادات النافذة
            if settings.get("maximize_window", False):
                self.showMaximized()
            else:
                width = settings.get("window_width", 1200)
                height = settings.get("window_height", 800)
                self.resize(width, height)

            # تحديث البيانات إذا تغيرت المجلدات
            self.refresh_data()

        except Exception as e:
            QMessageBox.warning(
                self, "تحذير",
                f"خطأ في تطبيق بعض الإعدادات:\n{e}"
            )

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        QMessageBox.about(
            self, "حول البرنامج",
            f"{APP_NAME}\n\n"
            "نظام شامل لإدارة خطابات المستأجرين\n"
            "يدعم إنشاء الخطابات من القوالب وإرسالها عبر واتساب\n\n"
            "المميزات:\n"
            "• إدارة كاملة للمستأجرين\n"
            "• إنشاء خطابات من قوالب Word\n"
            "• إرسال عبر واتساب\n"
            "• دعم كامل للغة العربية\n"
            "• قاعدة بيانات محلية آمنة\n\n"
            "تم التطوير باستخدام Python و PySide6\n"
            "الإصدار: 2.0.0"
        )
