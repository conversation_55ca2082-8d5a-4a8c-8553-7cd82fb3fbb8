# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للتطبيق
Main Application Window
"""

import sys
import logging
from datetime import datetime
from typing import List

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
    QTableWidget, QTableWidgetItem, QPushButton, QLineEdit, QLabel,
    QMessageBox, QHeaderView, QAbstractItemView, QSplitter,
    QGroupBox, QTextEdit, QComboBox, QDateEdit, QSpacerItem, QSizePolicy
)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QIcon, QFont, QAction

from config.settings import (
    APP_NAME, WINDOW_WIDTH, WINDOW_HEIGHT, WINDOW_MIN_WIDTH, WINDOW_MIN_HEIGHT
)
from database.database_manager import DatabaseManager
from database.models import Tenant
from utils.arabic_support import ArabicSupport
from ui.tenant_dialog import TenantDialog
from ui.letter_dialog import LetterDialog
from ui.template_dialog import TemplateDialog
from ui.settings_dialog import SettingsDialog


class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""

    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_tenants: List[Tenant] = []

        # تطبيق RTL على النافذة من البداية
        self.setLayoutDirection(Qt.RightToLeft)

        self.init_ui()
        self.setup_connections()
        self.load_tenants()
        self.load_archive()  # تحميل الأرشيف عند بدء التطبيق

        # إعداد دعم اللغة العربية والـ RTL
        ArabicSupport.setup_arabic_font(self)
        ArabicSupport.setup_rtl_layout(self)
        ArabicSupport.setup_advanced_rtl(self)

        # تطبيق التحسينات الاحترافية
        try:
            from utils.ui_enhancer import UIEnhancer
            UIEnhancer.enhance_main_window(self)
        except ImportError:
            pass

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle(APP_NAME)
        self.setGeometry(100, 100, WINDOW_WIDTH, WINDOW_HEIGHT)
        self.setMinimumSize(WINDOW_MIN_WIDTH, WINDOW_MIN_HEIGHT)

        # إنشاء القائمة الرئيسية
        self.create_menu_bar()

        # إنشاء شريط الأدوات
        self.create_toolbar()

        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)

        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # تبويب المستأجرين
        self.create_tenants_tab()

        # تبويب الخطابات
        self.create_letters_tab()

        # تبويب الأرشيف
        self.create_archive_tab()

        # شريط الحالة
        self.statusBar().showMessage("مرحباً بك في نظام إدارة خطابات المستأجرين")

    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()

        # قائمة ملف
        file_menu = menubar.addMenu("ملف")

        # إضافة مستأجر جديد
        add_tenant_action = QAction("إضافة مستأجر جديد", self)
        add_tenant_action.setShortcut("Ctrl+N")
        add_tenant_action.triggered.connect(self.add_tenant)
        file_menu.addAction(add_tenant_action)

        file_menu.addSeparator()

        # خروج
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # قائمة الخطابات
        letters_menu = menubar.addMenu("الخطابات")

        # إنشاء خطاب جديد
        new_letter_action = QAction("إنشاء خطاب جديد", self)
        new_letter_action.setShortcut("Ctrl+L")
        new_letter_action.triggered.connect(self.create_new_letter)
        letters_menu.addAction(new_letter_action)

        # قائمة الأدوات
        tools_menu = menubar.addMenu("أدوات")

        # إدارة القوالب
        templates_action = QAction("إدارة القوالب", self)
        templates_action.setShortcut("Ctrl+T")
        templates_action.triggered.connect(self.manage_templates)
        tools_menu.addAction(templates_action)

        # الإعدادات
        settings_action = QAction("الإعدادات", self)
        settings_action.setShortcut("Ctrl+,")
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")

        # حول البرنامج
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")

        # إضافة مستأجر
        add_tenant_btn = QPushButton("إضافة مستأجر")
        add_tenant_btn.clicked.connect(self.add_tenant)
        toolbar.addWidget(add_tenant_btn)

        toolbar.addSeparator()

        # إنشاء خطاب
        new_letter_btn = QPushButton("إنشاء خطاب")
        new_letter_btn.clicked.connect(self.create_new_letter)
        toolbar.addWidget(new_letter_btn)

        toolbar.addSeparator()

        # تحديث البيانات
        refresh_btn = QPushButton("تحديث")
        refresh_btn.clicked.connect(self.refresh_data)
        toolbar.addWidget(refresh_btn)

    def create_tenants_tab(self):
        """إنشاء تبويب المستأجرين"""
        tenants_widget = QWidget()
        layout = QVBoxLayout(tenants_widget)

        # شريط البحث والأدوات
        search_layout = QHBoxLayout()

        # البحث
        search_label = QLabel("البحث:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث بالاسم أو رقم الوحدة أو العمارة...")
        self.search_input.textChanged.connect(self.search_tenants)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)

        # أزرار الإدارة
        self.add_tenant_btn = QPushButton("إضافة مستأجر")
        self.edit_tenant_btn = QPushButton("تعديل")
        self.delete_tenant_btn = QPushButton("حذف")

        self.add_tenant_btn.clicked.connect(self.add_tenant)
        self.edit_tenant_btn.clicked.connect(self.edit_tenant)
        self.delete_tenant_btn.clicked.connect(self.delete_tenant)

        search_layout.addWidget(self.add_tenant_btn)
        search_layout.addWidget(self.edit_tenant_btn)
        search_layout.addWidget(self.delete_tenant_btn)

        layout.addLayout(search_layout)

        # جدول المستأجرين مع RTL كامل
        self.tenants_table = QTableWidget()
        self.tenants_table.setColumnCount(6)

        # تطبيق RTL على الجدول
        self.tenants_table.setLayoutDirection(Qt.RightToLeft)

        # إعداد الرؤوس مع تشكيل عربي
        headers = ["الاسم", "رقم الوحدة", "رقم العمارة", "رقم الهاتف", "البريد الإلكتروني", "ملاحظات"]
        shaped_headers = [ArabicSupport.reshape_arabic_text(header) for header in headers]
        self.tenants_table.setHorizontalHeaderLabels(shaped_headers)

        # إعداد الرأس للـ RTL
        header = self.tenants_table.horizontalHeader()
        if header:
            header.setLayoutDirection(Qt.RightToLeft)
            # تطبيق خط عربي على الرأس
            font = QFont("Tahoma", 11)
            font.setBold(True)
            font.setStyleHint(QFont.SansSerif)
            header.setFont(font)

        # إعداد الجدول
        header = self.tenants_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.tenants_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.tenants_table.setAlternatingRowColors(True)

        # إعداد دعم اللغة العربية والتحسينات للجدول
        ArabicSupport.setup_arabic_font(self.tenants_table)
        ArabicSupport.setup_rtl_layout(self.tenants_table)

        # تطبيق التحسينات الاحترافية وإصلاح عرض النصوص العربية
        try:
            from utils.ui_enhancer import UIEnhancer
            UIEnhancer._enhance_tables(self.tenants_table)
            UIEnhancer.fix_table_display(self.tenants_table)
        except ImportError:
            pass

        layout.addWidget(self.tenants_table)

        # إضافة التبويب
        self.tab_widget.addTab(tenants_widget, "المستأجرين")

    def create_letters_tab(self):
        """إنشاء تبويب الخطابات"""
        letters_widget = QWidget()
        layout = QVBoxLayout(letters_widget)

        # معلومات سريعة
        info_group = QGroupBox("معلومات سريعة")
        info_layout = QHBoxLayout(info_group)

        self.total_tenants_label = QLabel("إجمالي المستأجرين: 0")
        self.total_letters_label = QLabel("إجمالي الخطابات: 0")

        info_layout.addWidget(self.total_tenants_label)
        info_layout.addWidget(self.total_letters_label)
        info_layout.addStretch()

        layout.addWidget(info_group)

        # أزرار سريعة
        quick_actions_group = QGroupBox("إجراءات سريعة")
        quick_layout = QHBoxLayout(quick_actions_group)

        self.quick_letter_btn = QPushButton("إنشاء خطاب سريع")
        self.templates_btn = QPushButton("إدارة القوالب")
        self.export_btn = QPushButton("تصدير التقارير")

        self.quick_letter_btn.clicked.connect(self.create_new_letter)
        self.templates_btn.clicked.connect(self.manage_templates)
        self.export_btn.clicked.connect(self.export_reports)

        quick_layout.addWidget(self.quick_letter_btn)
        quick_layout.addWidget(self.templates_btn)
        quick_layout.addWidget(self.export_btn)
        quick_layout.addStretch()

        layout.addWidget(quick_actions_group)

        # مساحة فارغة
        layout.addStretch()

        # إضافة التبويب
        self.tab_widget.addTab(letters_widget, "الخطابات")

    def create_archive_tab(self):
        """إنشاء تبويب الأرشيف"""
        archive_widget = QWidget()
        layout = QVBoxLayout(archive_widget)

        # فلاتر البحث
        filter_layout = QHBoxLayout()

        filter_layout.addWidget(QLabel("من تاريخ:"))
        self.from_date = QDateEdit()
        self.from_date.setDate(QDate.currentDate().addDays(-30))
        filter_layout.addWidget(self.from_date)

        filter_layout.addWidget(QLabel("إلى تاريخ:"))
        self.to_date = QDateEdit()
        self.to_date.setDate(QDate.currentDate())
        filter_layout.addWidget(self.to_date)

        self.filter_btn = QPushButton("تطبيق الفلتر")
        filter_layout.addWidget(self.filter_btn)

        self.refresh_archive_btn = QPushButton("تحديث الأرشيف")
        filter_layout.addWidget(self.refresh_archive_btn)

        filter_layout.addStretch()

        layout.addLayout(filter_layout)

        # جدول الأرشيف
        self.archive_table = QTableWidget()
        self.archive_table.setColumnCount(5)
        self.archive_table.setHorizontalHeaderLabels([
            "رقم الخطاب", "اسم المستأجر", "نوع القالب", "تاريخ الإنشاء", "الحالة"
        ])

        # إعداد الجدول
        header = self.archive_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.archive_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.archive_table.setAlternatingRowColors(True)

        # إعداد دعم اللغة العربية
        ArabicSupport.setup_arabic_font(self.archive_table)
        ArabicSupport.setup_rtl_layout(self.archive_table)

        layout.addWidget(self.archive_table)

        # إضافة التبويب
        self.tab_widget.addTab(archive_widget, "الأرشيف")

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # تحديد صف في جدول المستأجرين
        self.tenants_table.selectionModel().selectionChanged.connect(
            self.on_tenant_selection_changed
        )

        # النقر المزدوج لتعديل المستأجر
        self.tenants_table.doubleClicked.connect(self.edit_tenant)

        # أزرار الأرشيف
        self.filter_btn.clicked.connect(self.filter_archive)
        self.refresh_archive_btn.clicked.connect(self.load_archive)

    def load_tenants(self):
        """تحميل بيانات المستأجرين"""
        try:
            self.current_tenants = self.db_manager.get_all_tenants()
            self.update_tenants_table()
            self.update_statistics()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات المستأجرين:\n{e}")

    def update_tenants_table(self):
        """تحديث جدول المستأجرين مع إصلاح كامل للنصوص العربية"""
        self.tenants_table.setRowCount(len(self.current_tenants))

        # تطبيق RTL على الجدول أولاً
        self.tenants_table.setLayoutDirection(Qt.RightToLeft)

        # إعداد الرأس للـ RTL
        header = self.tenants_table.horizontalHeader()
        if header:
            header.setLayoutDirection(Qt.RightToLeft)

        for row, tenant in enumerate(self.current_tenants):
            # إعداد البيانات مع تشكيل النصوص العربية
            name_text = tenant.name or ""
            notes_text = tenant.notes or ""

            # تشكيل النصوص العربية
            if name_text:
                name_text = ArabicSupport.reshape_arabic_text(name_text)
            if notes_text:
                notes_text = ArabicSupport.reshape_arabic_text(notes_text)

            # إنشاء العناصر
            name_item = QTableWidgetItem(name_text)
            unit_item = QTableWidgetItem(tenant.unit_number or "")
            building_item = QTableWidgetItem(tenant.building_number or "")
            phone_item = QTableWidgetItem(tenant.phone_number or "")
            email_item = QTableWidgetItem(tenant.email or "")
            notes_item = QTableWidgetItem(notes_text)

            # تطبيق إعدادات RTL والخط على كل عنصر
            items = [name_item, unit_item, building_item, phone_item, email_item, notes_item]
            for item in items:
                # محاذاة من اليمين لليسار (حسب الاقتراح)
                item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)

                # إعداد خط يدعم العربية (حسب الاقتراح: Arial, Tahoma, Amiri)
                font = QFont()
                # جرب الخطوط بالترتيب: Tahoma, Arial, Amiri
                font_families = ["Tahoma", "Arial", "Amiri", "Cairo", "Noto Naskh Arabic"]
                for font_family in font_families:
                    font.setFamily(font_family)
                    if font.exactMatch():
                        break

                font.setPointSize(12)  # حجم أكبر للوضوح
                font.setStyleHint(QFont.SansSerif)
                font.setStyleStrategy(QFont.PreferAntialias)
                font.setKerning(True)  # تحسين المسافات بين الأحرف
                item.setFont(font)

            # تخزين معرف المستأجر في العنصر الأول
            name_item.setData(Qt.UserRole, tenant.id)

            # إضافة العناصر للجدول
            self.tenants_table.setItem(row, 0, name_item)
            self.tenants_table.setItem(row, 1, unit_item)
            self.tenants_table.setItem(row, 2, building_item)
            self.tenants_table.setItem(row, 3, phone_item)
            self.tenants_table.setItem(row, 4, email_item)
            self.tenants_table.setItem(row, 5, notes_item)

        # تطبيق إعدادات RTL قوية على الجدول
        self.tenants_table.setLayoutDirection(Qt.RightToLeft)

        # إعداد الرأس مرة أخرى للتأكد
        header = self.tenants_table.horizontalHeader()
        if header:
            header.setLayoutDirection(Qt.RightToLeft)
            header.setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter)

        # إعداد الرأس العمودي أيضاً
        v_header = self.tenants_table.verticalHeader()
        if v_header:
            v_header.setLayoutDirection(Qt.RightToLeft)

        # تطبيق stylesheet محسن للجدول (حسب الاقتراحات)
        self.tenants_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                font-family: 'Tahoma', 'Arial', 'Amiri', 'Cairo';
                font-size: 12px;
                selection-background-color: #007bff;
            }
            QTableWidget::item {
                padding: 10px 8px;
                border-bottom: 1px solid #e9ecef;
                text-align: right;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QTableWidget::item:hover {
                background-color: #e3f2fd;
            }
            QHeaderView::section {
                text-align: right;
                padding: 12px 8px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-family: 'Tahoma', 'Arial', 'Amiri';
                font-size: 12px;
            }
            QHeaderView::section:hover {
                background-color: #e9ecef;
            }
        """)

        # تحديث عرض الجدول
        self.tenants_table.resizeColumnsToContents()
        self.tenants_table.horizontalHeader().setStretchLastSection(True)

    def update_statistics(self):
        """تحديث الإحصائيات"""
        total_tenants = len(self.current_tenants)
        self.total_tenants_label.setText(f"إجمالي المستأجرين: {total_tenants}")

        # TODO: إضافة إحصائية الخطابات عند تنفيذ الوظيفة
        self.total_letters_label.setText("إجمالي الخطابات: 0")

    def search_tenants(self):
        """البحث في المستأجرين"""
        search_term = self.search_input.text().strip()

        if not search_term:
            self.current_tenants = self.db_manager.get_all_tenants()
        else:
            self.current_tenants = self.db_manager.search_tenants(search_term)

        self.update_tenants_table()

    def on_tenant_selection_changed(self):
        """عند تغيير تحديد المستأجر"""
        selected_rows = self.tenants_table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0

        self.edit_tenant_btn.setEnabled(has_selection)
        self.delete_tenant_btn.setEnabled(has_selection)

    def get_selected_tenant(self) -> Tenant:
        """الحصول على المستأجر المحدد"""
        selected_rows = self.tenants_table.selectionModel().selectedRows()
        if not selected_rows:
            return None

        row = selected_rows[0].row()
        tenant_id = self.tenants_table.item(row, 0).data(Qt.UserRole)

        # البحث عن المستأجر في القائمة الحالية
        for tenant in self.current_tenants:
            if tenant.id == tenant_id:
                return tenant

        return None

    def add_tenant(self):
        """إضافة مستأجر جديد"""
        dialog = TenantDialog(self)
        if dialog.exec() == TenantDialog.Accepted:
            self.load_tenants()

    def edit_tenant(self):
        """تعديل مستأجر"""
        tenant = self.get_selected_tenant()
        if not tenant:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مستأجر للتعديل")
            return

        dialog = TenantDialog(self, tenant)
        if dialog.exec() == TenantDialog.Accepted:
            self.load_tenants()

    def delete_tenant(self):
        """حذف مستأجر"""
        tenant = self.get_selected_tenant()
        if not tenant:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مستأجر للحذف")
            return

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف المستأجر '{tenant.name}'؟\n"
            "سيتم حذف جميع الخطابات المرتبطة به أيضاً.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                if self.db_manager.delete_tenant(tenant.id):
                    QMessageBox.information(self, "نجح", "تم حذف المستأجر بنجاح")
                    self.load_tenants()
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في حذف المستأجر")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف المستأجر:\n{e}")

    def create_new_letter(self):
        """إنشاء خطاب جديد"""
        # التأكد من وجود مستأجرين
        if not self.current_tenants:
            QMessageBox.warning(
                self, "تحذير",
                "لا يوجد مستأجرين مسجلين.\nيرجى إضافة مستأجر أولاً."
            )
            return

        dialog = LetterDialog(self)
        if dialog.exec() == LetterDialog.Accepted:
            # تحديث البيانات بعد إنشاء الخطاب
            self.refresh_data()

    def manage_templates(self):
        """إدارة القوالب"""
        dialog = TemplateDialog(self)
        dialog.template_updated.connect(self.refresh_data)
        dialog.exec()

    def export_reports(self):
        """تصدير التقارير"""
        # TODO: تنفيذ تصدير التقارير
        QMessageBox.information(self, "قريباً", "ستتوفر هذه الميزة قريباً")

    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.load_tenants()
        self.load_archive()
        self.statusBar().showMessage("تم تحديث البيانات", 2000)

    def load_archive(self):
        """تحميل بيانات الأرشيف"""
        try:
            # الحصول على جميع الخطابات من قاعدة البيانات
            letters = self.db_manager.get_all_letters()
            self.update_archive_table(letters)
            self.statusBar().showMessage(f"تم تحميل {len(letters)} خطاب في الأرشيف", 2000)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الأرشيف:\n{e}")

    def filter_archive(self):
        """فلترة الأرشيف حسب التاريخ"""
        try:
            from_date = self.from_date.date().toString("yyyy-MM-dd")
            to_date = self.to_date.date().toString("yyyy-MM-dd")

            # الحصول على الخطابات المفلترة
            letters = self.db_manager.get_letters_by_date_range(from_date, to_date)
            self.update_archive_table(letters)
            self.statusBar().showMessage(f"تم العثور على {len(letters)} خطاب في الفترة المحددة", 2000)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فلترة الأرشيف:\n{e}")

    def update_archive_table(self, letters):
        """تحديث جدول الأرشيف"""
        self.archive_table.setRowCount(len(letters))

        # تطبيق RTL على جدول الأرشيف
        self.archive_table.setLayoutDirection(Qt.RightToLeft)

        # إعداد الرأس للـ RTL
        header = self.archive_table.horizontalHeader()
        if header:
            header.setLayoutDirection(Qt.RightToLeft)

        for row, letter in enumerate(letters):
            # الحصول على اسم المستأجر
            tenant = self.db_manager.get_tenant_by_id(letter.tenant_id)
            tenant_name = tenant.name if tenant else "غير معروف"

            # الحصول على اسم القالب
            template = self.db_manager.get_template_by_id(letter.template_id)
            template_name = template.name if template else "قالب افتراضي"

            # تشكيل النصوص العربية
            tenant_name = ArabicSupport.reshape_arabic_text(tenant_name)
            template_name = ArabicSupport.reshape_arabic_text(template_name)

            # إنشاء عناصر الجدول
            letter_number_item = QTableWidgetItem(letter.letter_number or f"خطاب-{letter.id}")
            tenant_name_item = QTableWidgetItem(tenant_name)
            template_name_item = QTableWidgetItem(template_name)
            created_date_item = QTableWidgetItem(letter.created_at[:10] if letter.created_at else "")
            status_item = QTableWidgetItem("مرسل")

            # تطبيق إعدادات RTL والخط على كل عنصر
            items = [letter_number_item, tenant_name_item, template_name_item, created_date_item, status_item]
            for item in items:
                item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)

                # إعداد خط عربي
                font = QFont()
                font_families = ["Tahoma", "Arial", "Amiri", "Cairo"]
                for font_family in font_families:
                    font.setFamily(font_family)
                    if font.exactMatch():
                        break

                font.setPointSize(11)
                font.setStyleHint(QFont.SansSerif)
                font.setStyleStrategy(QFont.PreferAntialias)
                item.setFont(font)

            # تخزين معرف الخطاب في العنصر الأول
            letter_number_item.setData(Qt.UserRole, letter.id)

            # إضافة العناصر للجدول
            self.archive_table.setItem(row, 0, letter_number_item)
            self.archive_table.setItem(row, 1, tenant_name_item)
            self.archive_table.setItem(row, 2, template_name_item)
            self.archive_table.setItem(row, 3, created_date_item)
            self.archive_table.setItem(row, 4, status_item)

        # تطبيق stylesheet محسن للجدول
        self.archive_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                font-family: 'Tahoma', 'Arial', 'Amiri';
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
                text-align: right;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                text-align: right;
                padding: 10px 8px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-family: 'Tahoma', 'Arial';
            }
        """)

        # تحديث عرض الجدول
        self.archive_table.resizeColumnsToContents()
        self.archive_table.horizontalHeader().setStretchLastSection(True)

    def show_settings(self):
        """عرض نافذة الإعدادات"""
        dialog = SettingsDialog(self)
        dialog.settings_changed.connect(self.apply_new_settings)
        dialog.exec()

    def apply_new_settings(self, settings: dict):
        """تطبيق الإعدادات الجديدة"""
        try:
            # تطبيق إعدادات الخط
            font_family = settings.get("font_family", "Tahoma")
            font_size = settings.get("font_size", 12)
            font_bold = settings.get("font_bold", False)

            from PySide6.QtGui import QFont
            font = QFont(font_family, font_size)
            if font_bold:
                font.setBold(True)

            self.setFont(font)

            # تطبيق إعدادات النافذة
            if settings.get("maximize_window", False):
                self.showMaximized()
            else:
                width = settings.get("window_width", 1200)
                height = settings.get("window_height", 800)
                self.resize(width, height)

            # تحديث البيانات إذا تغيرت المجلدات
            self.refresh_data()

        except Exception as e:
            QMessageBox.warning(
                self, "تحذير",
                f"خطأ في تطبيق بعض الإعدادات:\n{e}"
            )

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        QMessageBox.about(
            self, "حول البرنامج",
            f"{APP_NAME}\n\n"
            "نظام شامل لإدارة خطابات المستأجرين\n"
            "يدعم إنشاء الخطابات من القوالب وإرسالها عبر واتساب\n\n"
            "المميزات:\n"
            "• إدارة كاملة للمستأجرين\n"
            "• إنشاء خطابات من قوالب Word\n"
            "• إرسال عبر واتساب\n"
            "• دعم كامل للغة العربية\n"
            "• قاعدة بيانات محلية آمنة\n\n"
            "تم التطوير باستخدام Python و PySide6\n"
            "الإصدار: 2.0.0"
        )
