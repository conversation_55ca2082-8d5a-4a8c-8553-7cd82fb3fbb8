# -*- coding: utf-8 -*-
"""
نافذة إدارة المستأجرين
Tenant Management Dialog
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit, 
    QPushButton, QTextEdit, QLabel, QMessageBox, QGroupBox
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

from database.database_manager import DatabaseManager
from database.models import Tenant
from utils.arabic_support import ArabicSupport


class TenantDialog(QDialog):
    """نافذة إدارة المستأجرين"""
    
    def __init__(self, parent=None, tenant: Tenant = None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.tenant = tenant
        self.is_edit_mode = tenant is not None
        
        self.init_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_tenant_data()
        
        # إعداد دعم اللغة العربية
        ArabicSupport.setup_arabic_font(self)
        ArabicSupport.setup_rtl_layout(self)
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        title = "تعديل مستأجر" if self.is_edit_mode else "إضافة مستأجر جديد"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(500, 400)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # مجموعة البيانات الأساسية
        basic_info_group = QGroupBox("البيانات الأساسية")
        basic_layout = QFormLayout(basic_info_group)
        
        # حقول الإدخال
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("أدخل اسم المستأجر...")
        basic_layout.addRow("الاسم *:", self.name_input)
        
        self.unit_number_input = QLineEdit()
        self.unit_number_input.setPlaceholderText("رقم الوحدة...")
        basic_layout.addRow("رقم الوحدة *:", self.unit_number_input)
        
        self.building_number_input = QLineEdit()
        self.building_number_input.setPlaceholderText("رقم العمارة...")
        basic_layout.addRow("رقم العمارة *:", self.building_number_input)
        
        main_layout.addWidget(basic_info_group)
        
        # مجموعة معلومات الاتصال
        contact_info_group = QGroupBox("معلومات الاتصال")
        contact_layout = QFormLayout(contact_info_group)
        
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("رقم الهاتف...")
        contact_layout.addRow("رقم الهاتف:", self.phone_input)
        
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("البريد الإلكتروني...")
        contact_layout.addRow("البريد الإلكتروني:", self.email_input)
        
        main_layout.addWidget(contact_info_group)
        
        # مجموعة الملاحظات
        notes_group = QGroupBox("ملاحظات")
        notes_layout = QVBoxLayout(notes_group)
        
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("أي ملاحظات إضافية...")
        self.notes_input.setMaximumHeight(100)
        notes_layout.addWidget(self.notes_input)
        
        main_layout.addWidget(notes_group)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ")
        self.cancel_btn = QPushButton("إلغاء")
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)
        
        main_layout.addLayout(buttons_layout)
        
        # إعداد دعم اللغة العربية للحقول
        for widget in [self.name_input, self.unit_number_input, self.building_number_input,
                      self.phone_input, self.email_input, self.notes_input]:
            ArabicSupport.setup_arabic_font(widget)
            if isinstance(widget, (QLineEdit, QTextEdit)):
                ArabicSupport.setup_rtl_layout(widget)
    
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        self.save_btn.clicked.connect(self.save_tenant)
        self.cancel_btn.clicked.connect(self.reject)
        
        # التحقق من صحة البيانات عند التغيير
        self.name_input.textChanged.connect(self.validate_form)
        self.unit_number_input.textChanged.connect(self.validate_form)
        self.building_number_input.textChanged.connect(self.validate_form)
        
        # التحقق الأولي
        self.validate_form()
    
    def load_tenant_data(self):
        """تحميل بيانات المستأجر للتعديل"""
        if not self.tenant:
            return
        
        self.name_input.setText(self.tenant.name)
        self.unit_number_input.setText(self.tenant.unit_number)
        self.building_number_input.setText(self.tenant.building_number)
        self.phone_input.setText(self.tenant.phone_number)
        self.email_input.setText(self.tenant.email)
        self.notes_input.setPlainText(self.tenant.notes)
    
    def validate_form(self):
        """التحقق من صحة النموذج"""
        name = self.name_input.text().strip()
        unit_number = self.unit_number_input.text().strip()
        building_number = self.building_number_input.text().strip()
        
        is_valid = bool(name and unit_number and building_number)
        self.save_btn.setEnabled(is_valid)
        
        return is_valid
    
    def get_tenant_data(self) -> Tenant:
        """الحصول على بيانات المستأجر من النموذج"""
        tenant = Tenant()
        
        if self.is_edit_mode and self.tenant:
            tenant.id = self.tenant.id
            tenant.created_at = self.tenant.created_at
        
        tenant.name = self.name_input.text().strip()
        tenant.unit_number = self.unit_number_input.text().strip()
        tenant.building_number = self.building_number_input.text().strip()
        tenant.phone_number = self.phone_input.text().strip()
        tenant.email = self.email_input.text().strip()
        tenant.notes = self.notes_input.toPlainText().strip()
        
        return tenant
    
    def save_tenant(self):
        """حفظ بيانات المستأجر"""
        if not self.validate_form():
            QMessageBox.warning(self, "خطأ", "يرجى ملء جميع الحقول المطلوبة")
            return
        
        try:
            tenant_data = self.get_tenant_data()
            
            if self.is_edit_mode:
                # تحديث المستأجر الموجود
                success = self.db_manager.update_tenant(tenant_data)
                message = "تم تحديث بيانات المستأجر بنجاح" if success else "فشل في تحديث المستأجر"
            else:
                # إضافة مستأجر جديد
                tenant_id = self.db_manager.add_tenant(tenant_data)
                success = tenant_id > 0
                message = "تم إضافة المستأجر بنجاح" if success else "فشل في إضافة المستأجر"
            
            if success:
                QMessageBox.information(self, "نجح", message)
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", message)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ البيانات:\n{e}")
    
    def keyPressEvent(self, event):
        """التعامل مع ضغط المفاتيح"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            if self.save_btn.isEnabled():
                self.save_tenant()
        elif event.key() == Qt.Key_Escape:
            self.reject()
        else:
            super().keyPressEvent(event)
