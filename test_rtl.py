# -*- coding: utf-8 -*-
"""
اختبار دعم RTL والواجهة الاحترافية
RTL and Professional UI Test
"""

import sys
from pathlib import Path

# إعداد المسارات
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_rtl_support():
    """اختبار دعم RTL"""
    print("🔤 اختبار دعم RTL والعربية...")
    
    try:
        from utils.arabic_support import ArabicSupport
        
        # اختبار تشكيل النص العربي
        test_text = "مرحباً بكم في نظام إدارة خطابات المستأجرين"
        reshaped = ArabicSupport.reshape_arabic_text(test_text)
        
        if reshaped:
            print("✅ تشكيل النص العربي يعمل")
        else:
            print("❌ مشكلة في تشكيل النص العربي")
        
        # اختبار إصلاح عرض النصوص المعقدة
        complex_text = "الخطاب رقم: ١٢٣٤ - تاريخ: ٢٠٢٥/٠٥/٢٧"
        fixed_text = ArabicSupport.fix_arabic_text_rendering(complex_text)
        
        if fixed_text:
            print("✅ إصلاح النصوص المعقدة يعمل")
        else:
            print("❌ مشكلة في إصلاح النصوص المعقدة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار RTL: {e}")
        return False

def test_professional_styles():
    """اختبار الأنماط الاحترافية"""
    print("🎨 اختبار الأنماط الاحترافية...")
    
    try:
        from ui.professional_styles import ProfessionalStyles
        
        # اختبار الحصول على الأنماط
        main_style = ProfessionalStyles.get_main_window_style()
        button_styles = ProfessionalStyles.get_button_styles()
        table_style = ProfessionalStyles.get_table_style()
        complete_style = ProfessionalStyles.get_complete_professional_style()
        
        if all([main_style, button_styles, table_style, complete_style]):
            print("✅ جميع الأنماط الاحترافية متوفرة")
            print(f"   📏 حجم النمط الكامل: {len(complete_style)} حرف")
            return True
        else:
            print("❌ بعض الأنماط مفقودة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الأنماط: {e}")
        return False

def test_ui_enhancer():
    """اختبار محسن واجهة المستخدم"""
    print("🔧 اختبار محسن واجهة المستخدم...")
    
    try:
        from utils.ui_enhancer import UIEnhancer
        
        # اختبار الوظائف المتاحة
        methods = [
            'enhance_widget',
            'enhance_dialog', 
            'enhance_main_window',
            'setup_rtl_table',
            'create_professional_button'
        ]
        
        available_methods = []
        for method in methods:
            if hasattr(UIEnhancer, method):
                available_methods.append(method)
        
        print(f"✅ {len(available_methods)}/{len(methods)} وظيفة متوفرة")
        for method in available_methods:
            print(f"   ✓ {method}")
        
        return len(available_methods) == len(methods)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المحسن: {e}")
        return False

def test_simple_gui():
    """اختبار واجهة مستخدم بسيطة مع RTL"""
    print("🖼️ اختبار واجهة مستخدم بسيطة...")
    
    try:
        from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QLineEdit
        from PySide6.QtCore import Qt
        from utils.arabic_support import ArabicSupport
        from utils.ui_enhancer import UIEnhancer
        
        app = QApplication(sys.argv)
        
        # إعداد RTL للتطبيق
        ArabicSupport.setup_application_rtl(app)
        
        # إنشاء نافذة اختبار
        window = QMainWindow()
        window.setWindowTitle("اختبار RTL والأنماط الاحترافية")
        window.resize(600, 400)
        
        # إنشاء العناصر
        central_widget = QWidget()
        layout = QVBoxLayout(central_widget)
        
        # تسمية
        label = QLabel("مرحباً بكم في اختبار RTL")
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
        
        # حقل نص
        text_input = QLineEdit()
        text_input.setPlaceholderText("اكتب نصاً عربياً هنا...")
        layout.addWidget(text_input)
        
        # أزرار مختلفة
        success_btn = UIEnhancer.create_professional_button("حفظ", "success")
        danger_btn = UIEnhancer.create_professional_button("حذف", "danger")
        info_btn = UIEnhancer.create_professional_button("معلومات", "info")
        
        layout.addWidget(success_btn)
        layout.addWidget(danger_btn)
        layout.addWidget(info_btn)
        
        window.setCentralWidget(central_widget)
        
        # تطبيق التحسينات
        UIEnhancer.enhance_main_window(window)
        
        # عرض النافذة
        window.show()
        
        print("✅ تم إنشاء نافذة الاختبار بنجاح")
        print("   🎯 النافذة تدعم RTL كاملاً")
        print("   🎨 تم تطبيق الأنماط الاحترافية")
        print("   🔤 النصوص العربية تظهر بشكل صحيح")
        
        # إغلاق النافذة بعد ثانيتين
        from PySide6.QtCore import QTimer
        QTimer.singleShot(2000, window.close)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 بدء اختبار دعم RTL والواجهة الاحترافية")
    print("=" * 60)
    
    tests = [
        ("دعم RTL والعربية", test_rtl_support),
        ("الأنماط الاحترافية", test_professional_styles),
        ("محسن واجهة المستخدم", test_ui_enhancer),
        ("واجهة مستخدم بسيطة", test_simple_gui)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 اختبار: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ نجح اختبار: {test_name}")
                passed += 1
            else:
                print(f"❌ فشل اختبار: {test_name}")
        except Exception as e:
            print(f"💥 خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! RTL والأنماط الاحترافية تعمل بشكل مثالي")
        return 0
    else:
        print("⚠️ بعض الاختبارات فشلت - تحقق من الأخطاء أعلاه")
        return 1

if __name__ == "__main__":
    exit_code = main()
    
    print(f"\n🏁 انتهى الاختبار برمز: {exit_code}")
    if exit_code == 0:
        print("✅ جميع الميزات تعمل بشكل صحيح")
        print("🚀 يمكنك الآن تشغيل التطبيق بثقة: python run_rtl.py")
    else:
        print("❌ هناك مشاكل تحتاج إصلاح")
    
    sys.exit(exit_code)
