# -*- coding: utf-8 -*-
"""
أنماط احترافية للواجهة مع دعم RTL
Professional UI Styles with RTL Support
"""

class ProfessionalStyles:
    """فئة الأنماط الاحترافية"""
    
    @staticmethod
    def get_main_window_style():
        """نمط النافذة الرئيسية الاحترافي"""
        return """
            QMainWindow {
                background-color: #f8f9fa;
                color: #212529;
                direction: rtl;
            }
            
            QMainWindow::separator {
                background-color: #dee2e6;
                width: 2px;
                height: 2px;
            }
            
            QStatusBar {
                background-color: #e9ecef;
                border-top: 1px solid #dee2e6;
                padding: 5px;
                direction: rtl;
            }
            
            QStatusBar::item {
                border: none;
                direction: rtl;
            }
        """
    
    @staticmethod
    def get_toolbar_style():
        """نمط شريط الأدوات الاحترافي"""
        return """
            QToolBar {
                background-color: #ffffff;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 8px;
                spacing: 8px;
                direction: rtl;
            }
            
            QToolBar::separator {
                background-color: #dee2e6;
                width: 2px;
                margin: 5px;
            }
            
            QToolButton {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 12px 16px;
                margin: 2px;
                font-weight: bold;
                font-size: 11px;
                min-width: 100px;
                min-height: 40px;
            }
            
            QToolButton:hover {
                background-color: #e9ecef;
                border-color: #007bff;
                transform: translateY(-2px);
            }
            
            QToolButton:pressed {
                background-color: #007bff;
                color: white;
                transform: translateY(1px);
            }
            
            QToolButton:checked {
                background-color: #007bff;
                color: white;
                border-color: #0056b3;
            }
        """
    
    @staticmethod
    def get_button_styles():
        """أنماط الأزرار الاحترافية"""
        return """
            /* الأزرار الأساسية */
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 11px;
                min-width: 100px;
                min-height: 40px;
            }
            
            QPushButton:hover {
                background-color: #5a6268;
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }
            
            QPushButton:pressed {
                background-color: #545b62;
                transform: translateY(1px);
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }
            
            QPushButton:disabled {
                background-color: #e9ecef;
                color: #6c757d;
                transform: none;
                box-shadow: none;
            }
            
            /* أزرار النجاح */
            QPushButton[class="success"] {
                background-color: #28a745;
            }
            
            QPushButton[class="success"]:hover {
                background-color: #218838;
            }
            
            QPushButton[class="success"]:pressed {
                background-color: #1e7e34;
            }
            
            /* أزرار الخطر */
            QPushButton[class="danger"] {
                background-color: #dc3545;
            }
            
            QPushButton[class="danger"]:hover {
                background-color: #c82333;
            }
            
            QPushButton[class="danger"]:pressed {
                background-color: #bd2130;
            }
            
            /* أزرار التحذير */
            QPushButton[class="warning"] {
                background-color: #ffc107;
                color: #212529;
            }
            
            QPushButton[class="warning"]:hover {
                background-color: #e0a800;
            }
            
            QPushButton[class="warning"]:pressed {
                background-color: #d39e00;
            }
            
            /* أزرار المعلومات */
            QPushButton[class="info"] {
                background-color: #17a2b8;
            }
            
            QPushButton[class="info"]:hover {
                background-color: #138496;
            }
            
            QPushButton[class="info"]:pressed {
                background-color: #117a8b;
            }
            
            /* أزرار أساسية */
            QPushButton[class="primary"] {
                background-color: #007bff;
            }
            
            QPushButton[class="primary"]:hover {
                background-color: #0056b3;
            }
            
            QPushButton[class="primary"]:pressed {
                background-color: #004085;
            }
        """
    
    @staticmethod
    def get_table_style():
        """نمط الجداول الاحترافي"""
        return """
            QTableWidget {
                background-color: white;
                alternate-background-color: #f8f9fa;
                gridline-color: #dee2e6;
                border: 2px solid #e9ecef;
                border-radius: 12px;
                selection-background-color: #007bff;
                direction: rtl;
            }
            
            QTableWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid #e9ecef;
                text-align: right;
            }
            
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            
            QTableWidget::item:hover {
                background-color: #e3f2fd;
            }
            
            QHeaderView {
                background-color: #f8f9fa;
                direction: rtl;
            }
            
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 15px 10px;
                border: 1px solid #dee2e6;
                border-radius: 0px;
                font-weight: bold;
                font-size: 12px;
                color: #495057;
                text-align: right;
            }
            
            QHeaderView::section:hover {
                background-color: #dee2e6;
            }
            
            QHeaderView::section:pressed {
                background-color: #ced4da;
            }
            
            QTableWidget::corner {
                background-color: #e9ecef;
                border: 1px solid #dee2e6;
            }
        """
    
    @staticmethod
    def get_input_style():
        """نمط حقول الإدخال الاحترافي"""
        return """
            QLineEdit, QTextEdit, QPlainTextEdit {
                background-color: white;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 12px 16px;
                font-size: 11px;
                color: #495057;
                direction: rtl;
                text-align: right;
            }
            
            QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
                border-color: #007bff;
                background-color: #f8f9ff;
                box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
            }
            
            QLineEdit:hover, QTextEdit:hover, QPlainTextEdit:hover {
                border-color: #ced4da;
            }
            
            QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {
                background-color: #e9ecef;
                color: #6c757d;
                border-color: #e9ecef;
            }
            
            QComboBox {
                background-color: white;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 12px 16px;
                font-size: 11px;
                color: #495057;
                direction: rtl;
                text-align: right;
                min-height: 20px;
            }
            
            QComboBox:focus {
                border-color: #007bff;
                box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
            }
            
            QComboBox:hover {
                border-color: #ced4da;
            }
            
            QComboBox::drop-down {
                border: none;
                width: 30px;
                border-top-left-radius: 8px;
                border-bottom-left-radius: 8px;
            }
            
            QComboBox::down-arrow {
                image: none;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 6px solid #6c757d;
                margin-right: 8px;
            }
            
            QComboBox QAbstractItemView {
                background-color: white;
                border: 2px solid #007bff;
                border-radius: 8px;
                selection-background-color: #007bff;
                direction: rtl;
            }
        """
    
    @staticmethod
    def get_dialog_style():
        """نمط نوافذ الحوار الاحترافي"""
        return """
            QDialog {
                background-color: #ffffff;
                border: 2px solid #dee2e6;
                border-radius: 12px;
                direction: rtl;
            }
            
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #f8f9fa;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top right;
                padding: 5px 15px;
                background-color: white;
                color: #495057;
                border: 1px solid #e9ecef;
                border-radius: 5px;
            }
            
            QLabel {
                color: #495057;
                font-size: 11px;
                direction: rtl;
                text-align: right;
            }
            
            QCheckBox {
                direction: rtl;
                spacing: 10px;
                font-size: 11px;
                color: #495057;
            }
            
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 2px solid #dee2e6;
                border-radius: 4px;
                background-color: white;
            }
            
            QCheckBox::indicator:checked {
                background-color: #007bff;
                border-color: #007bff;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xMC42IDEuNEw0LjIgNy44TDEuNCA1IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
            
            QCheckBox::indicator:hover {
                border-color: #007bff;
            }
        """
    
    @staticmethod
    def get_complete_professional_style():
        """النمط الاحترافي الكامل"""
        return (
            ProfessionalStyles.get_main_window_style() +
            ProfessionalStyles.get_toolbar_style() +
            ProfessionalStyles.get_button_styles() +
            ProfessionalStyles.get_table_style() +
            ProfessionalStyles.get_input_style() +
            ProfessionalStyles.get_dialog_style()
        )
