@echo off
chcp 65001 >nul
title نظام إدارة خطابات المستأجرين - الإصدار 2.0

echo.
echo ========================================
echo   نظام إدارة خطابات المستأجرين v2.0
echo ========================================
echo.

echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث
    pause
    exit /b 1
)

echo ✅ Python متوفر

echo.
echo 🚀 تشغيل التطبيق المحسن...
echo.

python run_v2.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق
    echo.
    echo جرب الحلول التالية:
    echo 1. python update_app.py
    echo 2. pip install -r requirements.txt
    echo 3. python start_app.py
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق التطبيق بنجاح
)

pause
