# -*- coding: utf-8 -*-
"""
منشئ القوالب الافتراضية
Default Template Creator
"""

import os
from pathlib import Path
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn

from config.settings import TEMPLATES_DIR
from utils.file_utils import FileUtils


class TemplateCreator:
    """منشئ القوالب الافتراضية"""
    
    @staticmethod
    def create_default_template():
        """إنشاء القالب الافتراضي"""
        try:
            # التأكد من وجود مجلد القوالب
            FileUtils.ensure_directory_exists(str(TEMPLATES_DIR))
            
            # مسار القالب الافتراضي
            template_path = TEMPLATES_DIR / "قالب_افتراضي.docx"
            
            # إنشاء المستند
            doc = Document()
            
            # إعداد الصفحة
            section = doc.sections[0]
            section.page_height = Inches(11.69)  # A4
            section.page_width = Inches(8.27)
            section.left_margin = Inches(1)
            section.right_margin = Inches(1)
            section.top_margin = Inches(1)
            section.bottom_margin = Inches(1)
            
            # إعداد الخط الافتراضي
            style = doc.styles['Normal']
            font = style.font
            font.name = 'Tahoma'
            font.size = Inches(0.12)  # 12pt
            
            # إعداد الخط للنصوص المعقدة (العربية)
            r = style.element
            r.rPr.rFonts.set(qn('w:cs'), 'Tahoma')
            
            # رأس المستند
            header_paragraph = doc.add_paragraph()
            header_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            header_run = header_paragraph.add_run("إدارة العقار")
            header_run.bold = True
            header_run.font.size = Inches(0.16)  # 16pt
            
            # فاصل
            doc.add_paragraph()
            
            # العنوان
            title_paragraph = doc.add_paragraph()
            title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            title_run = title_paragraph.add_run("خطاب إداري")
            title_run.bold = True
            title_run.font.size = Inches(0.18)  # 18pt
            
            # فاصل
            doc.add_paragraph("=" * 50).alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # معلومات الخطاب
            info_paragraph = doc.add_paragraph()
            info_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            info_paragraph.add_run("رقم الخطاب: {LetterNo}\n")
            info_paragraph.add_run("التاريخ: {Date}\n")
            
            # فاصل
            doc.add_paragraph()
            
            # بيانات المستأجر
            tenant_paragraph = doc.add_paragraph()
            tenant_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            tenant_paragraph.add_run("إلى المحترم/ة: ").bold = True
            tenant_paragraph.add_run("{Name}\n")
            tenant_paragraph.add_run("رقم الوحدة: {Unit}\n")
            tenant_paragraph.add_run("رقم العمارة: {Building}\n")
            
            # فاصل
            doc.add_paragraph()
            
            # التحية
            greeting_paragraph = doc.add_paragraph()
            greeting_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            greeting_paragraph.add_run("السلام عليكم ورحمة الله وبركاته،")
            
            # فاصل
            doc.add_paragraph()
            
            # المحتوى
            content_paragraph = doc.add_paragraph()
            content_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            content_paragraph.add_run("الموضوع: ")
            content_paragraph.add_run("{Content}")
            
            # فاصل
            doc.add_paragraph()
            doc.add_paragraph()
            
            # الختام
            closing_paragraph = doc.add_paragraph()
            closing_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            closing_paragraph.add_run("وتفضلوا بقبول فائق الاحترام والتقدير،\n\n")
            
            # التوقيع
            signature_paragraph = doc.add_paragraph()
            signature_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            signature_run = signature_paragraph.add_run("إدارة العقار")
            signature_run.bold = True
            
            # فاصل
            doc.add_paragraph()
            doc.add_paragraph()
            
            # تذييل
            footer_paragraph = doc.add_paragraph()
            footer_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            footer_paragraph.add_run("للاستفسار يرجى التواصل معنا")
            
            # حفظ المستند
            doc.save(str(template_path))
            
            return str(template_path)
            
        except Exception as e:
            print(f"خطأ في إنشاء القالب الافتراضي: {e}")
            return None
    
    @staticmethod
    def create_warning_template():
        """إنشاء قالب إنذار"""
        try:
            template_path = TEMPLATES_DIR / "قالب_إنذار.docx"
            
            doc = Document()
            
            # إعداد الصفحة والخط (نفس الإعدادات السابقة)
            section = doc.sections[0]
            section.page_height = Inches(11.69)
            section.page_width = Inches(8.27)
            section.left_margin = Inches(1)
            section.right_margin = Inches(1)
            section.top_margin = Inches(1)
            section.bottom_margin = Inches(1)
            
            style = doc.styles['Normal']
            font = style.font
            font.name = 'Tahoma'
            font.size = Inches(0.12)
            
            r = style.element
            r.rPr.rFonts.set(qn('w:cs'), 'Tahoma')
            
            # رأس المستند
            header_paragraph = doc.add_paragraph()
            header_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            header_run = header_paragraph.add_run("إدارة العقار")
            header_run.bold = True
            header_run.font.size = Inches(0.16)
            
            doc.add_paragraph()
            
            # العنوان
            title_paragraph = doc.add_paragraph()
            title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            title_run = title_paragraph.add_run("إنذار نهائي")
            title_run.bold = True
            title_run.font.size = Inches(0.18)
            
            doc.add_paragraph("=" * 50).alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # معلومات الخطاب
            info_paragraph = doc.add_paragraph()
            info_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            info_paragraph.add_run("رقم الإنذار: {LetterNo}\n")
            info_paragraph.add_run("التاريخ: {Date}\n")
            
            doc.add_paragraph()
            
            # بيانات المستأجر
            tenant_paragraph = doc.add_paragraph()
            tenant_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            tenant_paragraph.add_run("إلى المستأجر/ة: ").bold = True
            tenant_paragraph.add_run("{Name}\n")
            tenant_paragraph.add_run("رقم الوحدة: {Unit}\n")
            tenant_paragraph.add_run("رقم العمارة: {Building}\n")
            
            doc.add_paragraph()
            
            # التحية
            greeting_paragraph = doc.add_paragraph()
            greeting_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            greeting_paragraph.add_run("السلام عليكم ورحمة الله وبركاته،")
            
            doc.add_paragraph()
            
            # المحتوى
            content_paragraph = doc.add_paragraph()
            content_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            content_paragraph.add_run("نحيطكم علماً بأنه: ")
            content_paragraph.add_run("{Content}")
            
            doc.add_paragraph()
            
            # تحذير
            warning_paragraph = doc.add_paragraph()
            warning_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            warning_run = warning_paragraph.add_run(
                "وعليه نحذركم من عدم الالتزام بما ورد أعلاه، "
                "وإلا سنضطر لاتخاذ الإجراءات القانونية اللازمة."
            )
            warning_run.bold = True
            
            doc.add_paragraph()
            doc.add_paragraph()
            
            # الختام
            closing_paragraph = doc.add_paragraph()
            closing_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            closing_paragraph.add_run("وتفضلوا بقبول فائق الاحترام والتقدير،\n\n")
            
            # التوقيع
            signature_paragraph = doc.add_paragraph()
            signature_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            signature_run = signature_paragraph.add_run("إدارة العقار")
            signature_run.bold = True
            
            # حفظ المستند
            doc.save(str(template_path))
            
            return str(template_path)
            
        except Exception as e:
            print(f"خطأ في إنشاء قالب الإنذار: {e}")
            return None
    
    @staticmethod
    def create_all_default_templates():
        """إنشاء جميع القوالب الافتراضية"""
        templates_created = []
        
        # القالب الافتراضي
        default_template = TemplateCreator.create_default_template()
        if default_template:
            templates_created.append(default_template)
        
        # قالب الإنذار
        warning_template = TemplateCreator.create_warning_template()
        if warning_template:
            templates_created.append(warning_template)
        
        return templates_created
